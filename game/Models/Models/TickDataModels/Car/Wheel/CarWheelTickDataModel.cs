using Models.Models.TickDataModels.Vehicle.Wheel;

namespace Models.Models.TickDataModels.Car.Wheel
{
    public class CarWheelTickDataModel
    {
        public readonly VehicleWheelGroundTickDataModel WheelGroundTickData = new();
        public readonly VehicleWheelLoadTickDataModel WheelLoadTickData = new();
        public readonly VehicleWheelLinkTickDataModel WheelLinkTickData = new();
        public readonly VehicleWheelSuspensionLimitTickDataModel WheelSuspensionLimitTickData = new();
        public readonly VehicleWheelSuspensionTickDataModel WheelSuspensionTickData = new();
        public readonly CarWheelFrictionTickDataModel WheelFrictionTickData = new();
        public readonly CarWheelBurnoutStateTickDataModel WheelBurnoutTickData = new();
    }
}