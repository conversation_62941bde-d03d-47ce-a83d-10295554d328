using Models.Models.PhysicsBodiesModel;
using Models.Models.PhysicsSceneModel;
using Models.Models.WorldEntitiesModel;

namespace Models.Models.KinematicObjectsControllerDestroyModel
{
    public interface IKinematicObjectsControllerDestroyModel
    {
        void DestroyLocation(IPhysicsSceneModel physicsSceneModel, IWorldEntitiesModel worldEntitiesModel, IPhysicsBodiesModel physicsBodiesModel);
        void DestroyPlot(IPhysicsSceneModel physicsSceneModel, IWorldEntitiesModel worldEntitiesModel, IPhysicsBodiesModel physicsBodiesModel);
    }
}