using System.Numerics;
using Framework.Replication.Data.Primitive;
using Models.Replication;
using Models.References.Weapon;
using Models.Utils.Extensions;

namespace Models.Models.ShotArgsModel
{
    public class ShotArgsModel : IShotArgsModel
    {
        private readonly IPrimitiveData<Vector3> _position;
        private readonly IPrimitiveData<Vector3> _direction;
        private readonly IPrimitiveData<WeaponDescription> _weapon;

        public ShotArgsModel(IPrimitiveData<Vector3> position, IPrimitiveData<Vector3> direction, IPrimitiveData<WeaponDescription> weapon)
        {
            _position = position;
            _direction = direction;
            _weapon = weapon;
        }
        
        public Vector3 Position => _position.Value;
        public Vector3 Direction => _direction.Value;
        public WeaponDescription Weapon => _weapon.Value;
        
        public void Set(in Vector3 position, in Vector3 direction, WeaponDescription weapon)
        {
            _position.TrySetValue(position, ReplicationUtil.Epsilon);
            _direction.TrySetValue(direction, ReplicationUtil.Epsilon);
            _weapon.TrySetValue(weapon);
        }
    }
}