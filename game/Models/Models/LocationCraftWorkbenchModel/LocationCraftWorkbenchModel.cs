using Framework.Replication.Data.Primitive;

namespace Models.Models.LocationCraftWorkbenchModel
{
    public class LocationCraftWorkbenchModel : ILocationCraftWorkbenchModel
    {
        private readonly IPrimitiveData<bool> _isCraftWorkbenchActive;

        public LocationCraftWorkbenchModel(IPrimitiveData<bool> isCraftWorkbenchActive)
        {
            _isCraftWorkbenchActive = isCraftWorkbenchActive;
        }
    
        public bool IsCraftWorkbenchActive
        {
            get => _isCraftWorkbenchActive.Value;
            set => _isCraftWorkbenchActive.Value = value;
        }
    }
}