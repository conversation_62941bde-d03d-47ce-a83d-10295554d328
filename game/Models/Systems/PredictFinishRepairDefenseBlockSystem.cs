using Framework.Ecs.System;
using Framework.Ecs.Tick;
using Models.Inventory;
using Models.Models.CharacterValidationModel;
using Models.Models.PlotBuildingDefenseBlockPanelModel;
using Models.Models.ProlongedInteractionModel;
using Models.Models.TransformModel;
using Models.Models.WorldEntitiesCollectionModel;

namespace Models.Systems
{
    public class PredictFinishRepairDefenseBlockSystem : System<(
        IProlongedInteractionModel repairDefenseBlockModel,
        ICharacterValidationModel validationModel,
        ITransformModel transformModel )>
    {
        
        private readonly IWorldEntitiesCollectionModel<IPlotBuildingDefenseBlockPanelModel> _plotBuildingDefenseBlockPanels;

        public PredictFinishRepairDefenseBlockSystem(IWorldEntitiesCollectionModel<IPlotBuildingDefenseBlockPanelModel> plotBuildingDefenseBlockPanels)
        {
            _plotBuildingDefenseBlockPanels = plotBuildingDefenseBlockPanels;
        }

        protected override void Tick(ITick tick)
        {
            long now = tick.Ts;
            for (int i = 0; i < _count; i++)
            {
                ref var component = ref _components[i];

                var repairModel = component.repairDefenseBlockModel;
                var validationModel = component.validationModel;
                var transformModel = component.transformModel;

                if (!repairModel.IsInProcess) continue;
                
                if (!validationModel.CanPerformIdleActions() ||
                    !_plotBuildingDefenseBlockPanels.TryGetModel(repairModel.EntityId, out var panelModel) || 
                    !InventoryRules.CheckInteractDistance(transformModel, panelModel.DescriptionModel.Value.Position) ||
                    now >= repairModel.EndTs)
                {
                    repairModel.Stop();
                }
            }
        }
    }
}