using System;
using System.Numerics;
using Framework.Ecs.System;
using Framework.Ecs.Tick;
using Models.MathUtils;
using Models.Models.HelicopterWheelModel;
using Models.Models.PhysicsSceneModel;
using Models.Models.TickDataModels.Vehicle;
using Models.Models.TickDataModels.Vehicle.Wheel;
using Models.Physics.PhysicsBodies;

namespace Models.Systems
{
    public class HelicopterControllerPreparePhysicsForSubTickSystem : System<(PhysicsBodyId bodyId, VehicleTransformTickDataModel transformTickData, VehicleLinksTickDataModel linksTickData, (IHelicopterWheelModel wheel, VehicleWheelLinkTickDataModel wheelLinkTickData)[] wheels)>
    {
        private readonly IPhysicsSceneModel _physicsSceneModel;

        public HelicopterControllerPreparePhysicsForSubTickSystem(IPhysicsSceneModel physicsSceneModel)
        {
            _physicsSceneModel = physicsSceneModel;
        }
    
        protected override void Tick(ITick tick)
        {
            for (int i = 0; i < _count; i++)
            {
                ref var component = ref _components[i];

                var bodyId = component.bodyId;
                var transformTickData = component.transformTickData;
                var linksTickData = component.linksTickData;
                var wheels = component.wheels;

                Vector3 helicopterPosition = _physicsSceneModel.GetBodyPosition(bodyId);
                Quaternion helicopterOrientation = _physicsSceneModel.GetBodyOrientation(bodyId);
            
                Span<float> transformMatrix = new Span<float>(transformTickData.TransformMatrix, 0, 16);
                Matrix4x4Extension.CreateTransformMatrix(helicopterPosition, helicopterOrientation, transformMatrix);
            
                CalculateLinksDirection(transformMatrix, linksTickData);

                foreach (var (wheel, wheelLinkTickData) in wheels)
                {
                    CalculateWheelLink(wheel, transformMatrix, wheelLinkTickData);
                }
            }
        }

        private void CalculateLinksDirection(Span<float> transformMatrix, VehicleLinksTickDataModel linksTickData)
        {
            Matrix4x4Extension.GetBasisByIndex(transformMatrix, 1, out float basisYX, out float basisYY, out float basisYZ);
            linksTickData.WheelsLinkDirectionX = -basisYX;
            linksTickData.WheelsLinkDirectionY = -basisYY;
            linksTickData.WheelsLinkDirectionZ = -basisYZ;
        }

        private void CalculateWheelLink(IHelicopterWheelModel wheel, Span<float> transformMatrix, VehicleWheelLinkTickDataModel wheelLinkTickData)
        {
            Vector3 linkLocalPosition = wheel.LinkLocalOffsetFromCenterOfMass;
            Matrix4x4Extension.TransformPointLocalToWorld(linkLocalPosition.X, linkLocalPosition.Y, linkLocalPosition.Z, transformMatrix,
                out float linkWorldPositionX, out float linkWorldPositionY, out float linkWorldPositionZ);
            Matrix4x4Extension.GetPosition(transformMatrix, out float carPositionX, out float carPositionY, out float carPositionZ);

            wheelLinkTickData.LinkPositionX = linkWorldPositionX;
            wheelLinkTickData.LinkPositionY = linkWorldPositionY;
            wheelLinkTickData.LinkPositionZ = linkWorldPositionZ;
            
            wheelLinkTickData.LinkOffsetX = linkWorldPositionX - carPositionX;
            wheelLinkTickData.LinkOffsetY = linkWorldPositionY - carPositionY;
            wheelLinkTickData.LinkOffsetZ = linkWorldPositionZ - carPositionZ;
        }
    }
}