using System;
using Framework.Ecs.System;
using Framework.Ecs.Tick;
using Models.CharacterController;
using Models.Models.CharacterController;
using Models.Models.PhysicsDebug.Data.BattleCharacter;
using Models.Models.PhysicsDebug.Models.PhysicsDebugEnabledModel;
using Models.Models.PhysicsDebug.Models.PhysicsDebugModel;
using Models.References.Character;
using Models.Utils.Collections.EventsSet;

namespace Models.Systems
{
    public class CharacterControllerExtraVerticalVelocitySystem : System<(int id, ICharacterControllerModel character, float longJumpStartVelocityY, float shortJumpStartVelocityY)>
    {
        private const float _eps = .00000001f;
        
        private readonly float _tickDeltaTime;
        private readonly IEventsSet<CharacterControllerVerticalExtraVelocityChangedEvent> _verticalExtraVelocityChangeEvents;
        private readonly IPhysicsDebugEnabledModel _physicsDebugEnabledModel;
        private readonly IPhysicsDebugModel _physicsDebugModel;

        public CharacterControllerExtraVerticalVelocitySystem(float tickDeltaTime, IEventsSet<CharacterControllerVerticalExtraVelocityChangedEvent> verticalExtraVelocityChangeEvents, IPhysicsDebugEnabledModel physicsDebugEnabledModel, IPhysicsDebugModel physicsDebugModel)
        {
            _tickDeltaTime = tickDeltaTime;
            _verticalExtraVelocityChangeEvents = verticalExtraVelocityChangeEvents;
            _physicsDebugEnabledModel = physicsDebugEnabledModel;
            _physicsDebugModel = physicsDebugModel;
        }
        
        protected override void Tick(ITick tick)
        {
            bool isDebugEnabled = _physicsDebugEnabledModel.IsEnabled;
        
            for (int i = 0; i < _count; i++)
            {
                ref var component = ref _components[i];

                var entityId = component.id;
                var character = component.character;
                var longJumpStartVelocityY = component.longJumpStartVelocityY;
                var shortJumpStartVelocityY = component.shortJumpStartVelocityY;

                PhysicsDebugBattleCharacterTickData physicsDebug = isDebugEnabled ? _physicsDebugModel.GetBattleCharacterTickData(entityId) : null;
            
                switch (character.CharacterMovementType)
                {
                    case CharacterMovementType.Disabled:
                        UpdateDisabledMovementVerticalVelocity(character, physicsDebug);
                        break;
                    case CharacterMovementType.Ground:
                        UpdateGroundMovementVerticalVelocity(character, physicsDebug);
                        break;
                    case CharacterMovementType.Air:
                        UpdateAirMovementVerticalVelocity(character, physicsDebug);
                        break;
                    case CharacterMovementType.StartJump:
                        UpdateStartJumpMovementVerticalVelocity(character, longJumpStartVelocityY, shortJumpStartVelocityY, physicsDebug);
                        break;
                    case CharacterMovementType.BunnyHop:
                        UpdateStartJumpMovementVerticalVelocity(character, longJumpStartVelocityY, shortJumpStartVelocityY, physicsDebug);
                        break;
                }

                WriteVelocityDataToDebug(physicsDebug, character.CharacterMovementType, character.ExtraVelocityY, character.ExtraIntegratedVelocityY);
            }
        }

        private void UpdateDisabledMovementVerticalVelocity(ICharacterControllerModel character, PhysicsDebugBattleCharacterTickData physicsDebug)
        {
            SetExtraVelocity(character, 0, 0, physicsDebug);
        }

        private void UpdateGroundMovementVerticalVelocity(ICharacterControllerModel character, PhysicsDebugBattleCharacterTickData physicsDebug)
        {
            SetExtraVelocity(character, 0, 0, physicsDebug);
        }

        private void UpdateAirMovementVerticalVelocity(ICharacterControllerModel character, PhysicsDebugBattleCharacterTickData physicsDebug)
        {
            RecalculateAirVerticalVelocity(character, character.ExtraVelocityY, out float extraVelocityY, out float extraIntegratedVelocityY);
            SetExtraVelocity(character, extraVelocityY, extraIntegratedVelocityY, physicsDebug);
        }

        private void UpdateStartJumpMovementVerticalVelocity(ICharacterControllerModel character, float longJumpStartVelocityY, float shortJumpStartVelocityY, PhysicsDebugBattleCharacterTickData physicsDebug)
        {
            float extraVelocityY = GetJumpStartVelocityY(character.JumpEvent, longJumpStartVelocityY, shortJumpStartVelocityY);
            RecalculateAirVerticalVelocity(character, extraVelocityY, out extraVelocityY, out float extraIntegratedVelocityY);
            SetExtraVelocity(character, extraVelocityY, extraIntegratedVelocityY, physicsDebug);
        }

        private void RecalculateAirVerticalVelocity(ICharacterControllerModel character, float oldExtraVelocityY, out float newExtraVelocityY, out float newExtraIntegratedVelocityY)
        {
            float characterGravityMagnitude = character.State == CharacterControllerStateDescription.Rising ? character.Description.RisingGravityMagnitude : character.Description.FallingGravityMagnitude;
            character.IntegrateVelocityY(oldExtraVelocityY, -characterGravityMagnitude, _tickDeltaTime, out newExtraVelocityY, out newExtraIntegratedVelocityY);

            float maxFallingVelocity = -character.Description.MaxFallingSpeed;
            if (newExtraVelocityY < maxFallingVelocity)
            {
                newExtraVelocityY = maxFallingVelocity;
            }
            if (newExtraIntegratedVelocityY < maxFallingVelocity)
            {
                newExtraIntegratedVelocityY = maxFallingVelocity;
            }
        }

        private void SetExtraVelocity(ICharacterControllerModel character, float extraVelocityY, float extraIntegratedVelocityY, PhysicsDebugBattleCharacterTickData physicsDebug)
        {
            bool isChanged = false;

            float oldExtraVelocityY = character.ExtraVelocityY;
            float oldExtraIntegratedVelocityY = character.ExtraIntegratedVelocityY;
        
            if (Math.Abs(oldExtraVelocityY - extraVelocityY) > _eps)
            {
                character.ExtraVelocityY = extraVelocityY;
                isChanged = true;
            }

            if (Math.Abs(oldExtraIntegratedVelocityY - extraIntegratedVelocityY) > _eps)
            {
                character.ExtraIntegratedVelocityY = extraIntegratedVelocityY;
                isChanged = true;
            }

            if (isChanged)
            {
                ref CharacterControllerVerticalExtraVelocityChangedEvent eventData = ref _verticalExtraVelocityChangeEvents.GetEvent(_verticalExtraVelocityChangeEvents.Add());
                eventData.BodyId = character.BodyId;
                eventData.OldExtraVelocityY = oldExtraVelocityY;
                eventData.OldExtraIntegratedVelocityY = oldExtraIntegratedVelocityY;
                eventData.NewExtraVelocityY = extraVelocityY;
                eventData.NewExtraIntegratedVelocityY = extraIntegratedVelocityY;

                WriteVerticalVelocityChangeEventDataToDebug(physicsDebug, ref eventData);
            }
        }

        private float GetJumpStartVelocityY(JumpEvent jumpEvent, float longJumpStartVelocityY, float shortJumpStartVelocityY)
        {
            return jumpEvent switch
            {
                JumpEvent.LongJump => longJumpStartVelocityY,
                JumpEvent.ShortJump => shortJumpStartVelocityY,
                _ => throw new NotSupportedException()
            };
        }

        private static void WriteVelocityDataToDebug(PhysicsDebugBattleCharacterTickData physicsDebug, CharacterMovementType movementType, float extraVelocityY, float extraIntegratedVelocityY)
        {
            if (physicsDebug != null)
            {
                physicsDebug.ExtraVerticalVelocityMovementType = movementType;
                physicsDebug.ExtraVelocityY = extraVelocityY;
                physicsDebug.ExtraIntegratedVelocityY = extraIntegratedVelocityY;
            }
        }

        private static void WriteVerticalVelocityChangeEventDataToDebug(PhysicsDebugBattleCharacterTickData physicsDebug, ref CharacterControllerVerticalExtraVelocityChangedEvent eventData)
        {
            if (physicsDebug != null)
            {
                physicsDebug.ExtraVelocityYChangeEvent = true;
                physicsDebug.ExtraVelocityYChangeEventOldValue = eventData.OldExtraVelocityY;
                physicsDebug.ExtraIntegratedVelocityYChangeEventOldValue = eventData.OldExtraIntegratedVelocityY;
                physicsDebug.ExtraVelocityYChangeEventNewValue = eventData.NewExtraVelocityY;
                physicsDebug.ExtraIntegratedVelocityYChangeEventNewValue = eventData.NewExtraIntegratedVelocityY;
            }
        }
    }
}