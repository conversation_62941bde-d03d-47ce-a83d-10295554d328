using Framework.Ecs.System;
using Framework.Ecs.Tick;
using Models.Inventory;
using Models.Models.CharacterValidationModel;
using Models.Models.HangarOwnerModel;
using Models.Models.InventoryModel;
using Models.Models.IPartyMemberAccessModel;
using Models.Models.PlotBuildingDefenseBlockPanelModel;
using Models.Models.ProlongedInteractionModel;
using Models.Models.TransformModel;
using Models.Models.WorldEntitiesCollectionModel;
using Models.Models.WorldEntitiesModel;

namespace Models.Systems
{
    public class PredictFinishRaidDefenseBlockSystem : System<(
        int id, 
        IProlongedInteractionModel raidDefenseBlockModel,
        IInventoryModel inventoryModel,
        ICharacterValidationModel validationModel,
        ITransformModel transformModel )>
    {
         
        private readonly IWorldEntitiesCollectionModel<IPlotBuildingDefenseBlockPanelModel> _plotBuildingDefenseBlockPanels;

        private readonly IWorldEntitiesModel _worldEntitiesModel;
        private readonly IHangarOwnerModel _hangarOwnerModel;
        private readonly IPartyMemberAccessModel _partyAccess;
            
        public PredictFinishRaidDefenseBlockSystem(IWorldEntitiesModel worldEntitiesModel, IHangarOwnerModel hangarOwnerModel, IPartyMemberAccessModel partyAccess)
        {
            _worldEntitiesModel = worldEntitiesModel;
            _plotBuildingDefenseBlockPanels = worldEntitiesModel.PlotBuildingDefenseBlockPanels;
            _hangarOwnerModel = hangarOwnerModel;
            _partyAccess = partyAccess;
        }

        protected override void Tick(ITick tick)
        {
            long now = tick.Ts;
            for (int i = 0; i < _count; i++)
            {
                ref var component = ref _components[i];

                var characterId = component.id;
                var raidModel = component.raidDefenseBlockModel;
                var inventoryModel = component.inventoryModel;
                var validationModel = component.validationModel;
                var transformModel = component.transformModel;

                if (!raidModel.IsInProcess) continue;
                
                if (!validationModel.CanPerformIdleActions() ||
                    !_plotBuildingDefenseBlockPanels.TryGetModel(raidModel.EntityId, out var panelModel) || 
                    !InventoryRules.CheckInteractDistance(transformModel, panelModel.DescriptionModel.Value.Position))
                {
                    raidModel.Stop();
                    continue;
                }
                
                if (now < raidModel.EndTs)
                    continue;

                if (!DecoderItemRules.CanRaidDefenseBlock(characterId, panelModel, _worldEntitiesModel, _hangarOwnerModel, _partyAccess) ||
                    !DecoderItemRules.TryGetDecoderItemSlot(inventoryModel, out var decoderSlot))
                {
                    raidModel.Stop();
                    continue;
                }
                
                if (decoderSlot.StackModel.Value > 1)
                {
                    decoderSlot.StackModel.Value -= 1;
                }
                else
                {
                    decoderSlot.Unset();
                }
                
                raidModel.Stop();
            }
        }
    }
}