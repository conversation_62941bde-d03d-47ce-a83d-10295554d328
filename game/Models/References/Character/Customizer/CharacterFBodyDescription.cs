using System;
using Framework.Replication.Enum;
using Framework.Replication.EnumItem;

namespace Models.References.Character.Customizer
{
    public class CharacterFBodyDescription : EnumItem
    {
        private static readonly Enum<CharacterFBodyDescription> _enum = new();
        public static IEnum<CharacterFBodyDescription> Enum => _enum;

        private static int _notFreeCount;
        public static int NotFreeCount => _notFreeCount;

        private CharacterFBodyDescription(string id, bool isAvailable, int cryptoCoinsPrice, int imprisonmentYearsRequired) : base(id, _enum.Count)
        {
            if (isAvailable && cryptoCoinsPrice != 0)
            {
                throw new ArgumentException(id);
            }
            
            if (cryptoCoinsPrice == 0 && imprisonmentYearsRequired > 0)
            {
                throw new ArgumentException(id);
            }

            _enum.Add(this);

            IsAvailable = isAvailable;
            CryptoCoinsPrice = cryptoCoinsPrice;
            ImprisonmentYearsRequired = imprisonmentYearsRequired;

            if (!isAvailable)
            {
                _notFreeCount++;
            }
        }

        public bool IsAvailable { get; }
        public int CryptoCoinsPrice { get; }
        public int ImprisonmentYearsRequired { get; }

        public bool CanBuy => CryptoCoinsPrice != 0;
        public (int cryptoCoinsPrice, int imprisonmentYearsRequired) BuyParams => (CryptoCoinsPrice, ImprisonmentYearsRequired);

        public static CharacterFBodyDescription Body2Type1Paint1 { get; } = new CharacterFBodyDescription("f_body_2_type_1_paint_1", 
            isAvailable: true,
            cryptoCoinsPrice: 0,
            imprisonmentYearsRequired: 0
        );
        public static CharacterFBodyDescription Body3Type1Paint1 { get; } = new CharacterFBodyDescription("f_body_3_type_1_paint_1", 
            isAvailable: true,
            cryptoCoinsPrice: 0,
            imprisonmentYearsRequired: 0
        );
		public static CharacterFBodyDescription Body3Type2Paint1 { get; } = new CharacterFBodyDescription("f_body_3_type_2_paint_1", 
            isAvailable: true,
            cryptoCoinsPrice: 0,
            imprisonmentYearsRequired: 0
        );
		public static CharacterFBodyDescription Body3Type2Paint2 { get; } = new CharacterFBodyDescription("f_body_3_type_2_paint_2", 
            isAvailable: true,
            cryptoCoinsPrice: 0,
            imprisonmentYearsRequired: 0
        );
		public static CharacterFBodyDescription Body3Type2Paint3 { get; } = new CharacterFBodyDescription("f_body_3_type_2_paint_3", 
            isAvailable: true,
            cryptoCoinsPrice: 0,
            imprisonmentYearsRequired: 0
        );
		public static CharacterFBodyDescription Body3Type2Paint4 { get; } = new CharacterFBodyDescription("f_body_3_type_2_paint_4", 
            isAvailable: true,
            cryptoCoinsPrice: 0,
            imprisonmentYearsRequired: 0
        );
        public static CharacterFBodyDescription Body5Type1Paint1 { get; } = new CharacterFBodyDescription("f_body_5_type_1_paint_1", 
            isAvailable: false,
            cryptoCoinsPrice: 50,
            imprisonmentYearsRequired: 1
        );
        public static CharacterFBodyDescription Body6Type1Paint1 { get; } = new CharacterFBodyDescription("f_body_6_type_1_paint_1", 
            isAvailable: false,
            cryptoCoinsPrice: 20,
            imprisonmentYearsRequired: 0
        );
        public static CharacterFBodyDescription Body6Type1Paint2 { get; } = new CharacterFBodyDescription("f_body_6_type_1_paint_2", 
            isAvailable: false,
            cryptoCoinsPrice: 20,
            imprisonmentYearsRequired: 0
        );
        public static CharacterFBodyDescription Body6Type1Paint3 { get; } = new CharacterFBodyDescription("f_body_6_type_1_paint_3", 
            isAvailable: false,
            cryptoCoinsPrice: 20,
            imprisonmentYearsRequired: 0
        );
        public static CharacterFBodyDescription Body6Type1Paint4 { get; } = new CharacterFBodyDescription("f_body_6_type_1_paint_4", 
            isAvailable: false,
            cryptoCoinsPrice: 20,
            imprisonmentYearsRequired: 0
        );
        public static CharacterFBodyDescription Body8Type1Paint1 { get; } = new CharacterFBodyDescription("f_body_8_type_1_paint_1", 
            isAvailable: false,
            cryptoCoinsPrice: 50,
            imprisonmentYearsRequired: 0
        );
        public static CharacterFBodyDescription Body8Type1Paint2 { get; } = new CharacterFBodyDescription("f_body_8_type_1_paint_2", 
            isAvailable: false,
            cryptoCoinsPrice: 50,
            imprisonmentYearsRequired: 0
        );
        public static CharacterFBodyDescription Body8Type1Paint3 { get; } = new CharacterFBodyDescription("f_body_8_type_1_paint_3", 
            isAvailable: false,
            cryptoCoinsPrice: 50,
            imprisonmentYearsRequired: 0
        );
        public static CharacterFBodyDescription Body8Type1Paint4 { get; } = new CharacterFBodyDescription("f_body_8_type_1_paint_4", 
            isAvailable: false,
            cryptoCoinsPrice: 50,
            imprisonmentYearsRequired: 0
        );
        public static CharacterFBodyDescription Body9Type1Paint1 { get; } = new CharacterFBodyDescription("f_body_9_type_1_paint_1", 
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );
        public static CharacterFBodyDescription Body9Type1Paint2 { get; } = new CharacterFBodyDescription("f_body_9_type_1_paint_2", 
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );
        public static CharacterFBodyDescription Body9Type1Paint3 { get; } = new CharacterFBodyDescription("f_body_9_type_1_paint_3", 
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );
        public static CharacterFBodyDescription Body9Type1Paint4 { get; } = new CharacterFBodyDescription("f_body_9_type_1_paint_4", 
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );
        public static CharacterFBodyDescription Body9Type2Paint1 { get; } = new CharacterFBodyDescription("f_body_9_type_2_paint_1", 
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );
		public static CharacterFBodyDescription Body10Type1Paint1 { get; } = new CharacterFBodyDescription("f_body_10_type_1_paint_1", 
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );
		public static CharacterFBodyDescription Body10Type2Paint1 { get; } = new CharacterFBodyDescription("f_body_10_type_2_paint_1", 
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );
		public static CharacterFBodyDescription Body10Type2Paint2 { get; } = new CharacterFBodyDescription("f_body_10_type_2_paint_2", 
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );
		public static CharacterFBodyDescription Body11Type1Paint1 { get; } = new CharacterFBodyDescription("f_body_11_type_1_paint_1", 
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );
		public static CharacterFBodyDescription Body11Type1Paint2 { get; } = new CharacterFBodyDescription("f_body_11_type_1_paint_2", 
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );
		public static CharacterFBodyDescription Body11Type1Paint3 { get; } = new CharacterFBodyDescription("f_body_11_type_1_paint_3", 
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );
		public static CharacterFBodyDescription Body11Type1Paint4 { get; } = new CharacterFBodyDescription("f_body_11_type_1_paint_4", 
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );
        public static CharacterFBodyDescription Body14Type1Paint1 { get; } = new CharacterFBodyDescription("f_body_14_type_1_paint_1", 
            isAvailable: false,
            cryptoCoinsPrice: 50,
            imprisonmentYearsRequired: 0
        );
        public static CharacterFBodyDescription Body14Type1Paint2 { get; } = new CharacterFBodyDescription("f_body_14_type_1_paint_2", 
            isAvailable: false,
            cryptoCoinsPrice: 50,
            imprisonmentYearsRequired: 0
        );
        public static CharacterFBodyDescription Body14Type1Paint3 { get; } = new CharacterFBodyDescription("f_body_14_type_1_paint_3", 
            isAvailable: false,
            cryptoCoinsPrice: 50,
            imprisonmentYearsRequired: 0
        );
        public static CharacterFBodyDescription Body14Type1Paint4 { get; } = new CharacterFBodyDescription("f_body_14_type_1_paint_4", 
            isAvailable: false,
            cryptoCoinsPrice: 50,
            imprisonmentYearsRequired: 0
        );
        public static CharacterFBodyDescription Body16Type1Paint1 { get; } = new CharacterFBodyDescription("f_body_16_type_1_paint_1", 
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );
		public static CharacterFBodyDescription Body16Type1Paint2 { get; } = new CharacterFBodyDescription("f_body_16_type_1_paint_2", 
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );
		public static CharacterFBodyDescription Body16Type1Paint3 { get; } = new CharacterFBodyDescription("f_body_16_type_1_paint_3", 
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );
        public static CharacterFBodyDescription Body17Type1Paint1 { get; } = new CharacterFBodyDescription("f_body_17_type_1_paint_1", 
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );
        public static CharacterFBodyDescription Body18Type1Paint1 { get; } = new CharacterFBodyDescription("f_body_18_type_1_paint_1", 
            isAvailable: false,
            cryptoCoinsPrice: 50,
            imprisonmentYearsRequired: 0
        );
        public static CharacterFBodyDescription Body18Type1Paint2 { get; } = new CharacterFBodyDescription("f_body_18_type_1_paint_2", 
            isAvailable: false,
            cryptoCoinsPrice: 50,
            imprisonmentYearsRequired: 0
        );
        public static CharacterFBodyDescription Body18Type1Paint3 { get; } = new CharacterFBodyDescription("f_body_18_type_1_paint_3", 
            isAvailable: false,
            cryptoCoinsPrice: 50,
            imprisonmentYearsRequired: 0
        );
        public static CharacterFBodyDescription Body18Type1Paint4 { get; } = new CharacterFBodyDescription("f_body_18_type_1_paint_4", 
            isAvailable: false,
            cryptoCoinsPrice: 50,
            imprisonmentYearsRequired: 0
        );
        public static CharacterFBodyDescription Body19Type1Paint1 { get; } = new CharacterFBodyDescription("f_body_19_type_1_paint_1", 
            isAvailable: false,
            cryptoCoinsPrice: 20,
            imprisonmentYearsRequired: 0
        );
		public static CharacterFBodyDescription Body19Type2Paint1 { get; } = new CharacterFBodyDescription("f_body_19_type_2_paint_1", 
            isAvailable: false,
            cryptoCoinsPrice: 20,
            imprisonmentYearsRequired: 0
        );
		public static CharacterFBodyDescription Body19Type2Paint2 { get; } = new CharacterFBodyDescription("f_body_19_type_2_paint_2", 
            isAvailable: false,
            cryptoCoinsPrice: 20,
            imprisonmentYearsRequired: 0
        );
		public static CharacterFBodyDescription Body19Type2Paint3 { get; } = new CharacterFBodyDescription("f_body_19_type_2_paint_3", 
            isAvailable: false,
            cryptoCoinsPrice: 20,
            imprisonmentYearsRequired: 0
        );
        public static CharacterFBodyDescription Body20Type1Paint1 { get; } = new CharacterFBodyDescription("f_body_20_type_1_paint_1", 
            isAvailable: false,
            cryptoCoinsPrice: 10,
            imprisonmentYearsRequired: 0
        );
        public static CharacterFBodyDescription Body20Type2Paint1 { get; } = new CharacterFBodyDescription("f_body_20_type_2_paint_1", 
            isAvailable: false,
            cryptoCoinsPrice: 10,
            imprisonmentYearsRequired: 0
        );
        public static CharacterFBodyDescription Body21Type1Paint1 { get; } = new CharacterFBodyDescription("f_body_21_type_1_paint_1", 
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );
		public static CharacterFBodyDescription Body22Type1Paint1 { get; } = new CharacterFBodyDescription("f_body_22_type_1_paint_1", 
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );
        public static CharacterFBodyDescription Body22Type2Paint1 { get; } = new CharacterFBodyDescription("f_body_22_type_2_paint_1", 
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );
        public static CharacterFBodyDescription Body23Type1Paint1 { get; } = new CharacterFBodyDescription("f_body_23_type_1_paint_1", 
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );		
		public static CharacterFBodyDescription Body23Type1Paint2 { get; } = new CharacterFBodyDescription("f_body_23_type_1_paint_2", 
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );
        public static CharacterFBodyDescription Body24Type1Paint1 { get; } = new CharacterFBodyDescription("f_body_24_type_1_paint_1", 
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );        
        public static CharacterFBodyDescription Body24Type1Paint2 { get; } = new CharacterFBodyDescription("f_body_24_type_1_paint_2", 
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );        
        public static CharacterFBodyDescription Body24Type1Paint3 { get; } = new CharacterFBodyDescription("f_body_24_type_1_paint_3", 
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );
		public static CharacterFBodyDescription Body25Type1Paint1 { get; } = new CharacterFBodyDescription("f_body_25_type_1_paint_1", 
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );
		public static CharacterFBodyDescription Body26Type2Paint1 { get; } = new CharacterFBodyDescription("f_body_26_type_2_paint_1", 
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );
		public static CharacterFBodyDescription Body27Type1Paint1 { get; } = new CharacterFBodyDescription("f_body_27_type_1_paint_1", 
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );
		public static CharacterFBodyDescription Body27Type1Paint2 { get; } = new CharacterFBodyDescription("f_body_27_type_1_paint_2", 
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );
		public static CharacterFBodyDescription Body28Type1Paint1 { get; } = new CharacterFBodyDescription("f_body_28_type_1_paint_1", 
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );
		public static CharacterFBodyDescription Body29Type1Paint1 { get; } = new CharacterFBodyDescription("f_body_29_type_1_paint_1", 
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );
		public static CharacterFBodyDescription Body30Type1Paint1 { get; } = new CharacterFBodyDescription("f_body_30_type_1_paint_1", 
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );
		public static CharacterFBodyDescription Body31Type1Paint1 { get; } = new CharacterFBodyDescription("f_body_31_type_1_paint_1", 
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );
		public static CharacterFBodyDescription Body31Type2Paint1 { get; } = new CharacterFBodyDescription("f_body_31_type_2_paint_1", 
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );
		public static CharacterFBodyDescription Body32Type1Paint1 { get; } = new CharacterFBodyDescription("f_body_32_type_1_paint_1", 
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );
		public static CharacterFBodyDescription Body33Type1Paint1 { get; } = new CharacterFBodyDescription("f_body_33_type_1_paint_1", 
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );
        public static CharacterFBodyDescription Body34Type1Paint1 { get; } = new CharacterFBodyDescription("f_body_34_type_1_paint_1", 
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );        
        public static CharacterFBodyDescription Body34Type1Paint2 { get; } = new CharacterFBodyDescription("f_body_34_type_1_paint_2", 
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );
		public static CharacterFBodyDescription PrisonBody1 { get; } = new CharacterFBodyDescription("f_prisonbody_1",
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );	
        public static CharacterFBodyDescription PrisonBody2 { get; } = new CharacterFBodyDescription("f_prisonbody_2",
            isAvailable: false,
            cryptoCoinsPrice: 100,
            imprisonmentYearsRequired: 0
        );
    }
}