using Framework.Replication.Enum;
using Framework.Replication.EnumItem;

namespace Models.References.Character.Customizer
{
    public class CharacterEyeColorDescription : EnumItem
    {
        private static readonly Enum<CharacterEyeColorDescription> _enum = new();
        public static IEnum<CharacterEyeColorDescription> Enum => _enum;
    
        private CharacterEyeColorDescription(string id) : base(id, _enum.Count)
        {
            _enum.Add(this);
        }

        public static CharacterEyeColorDescription Color0 = new("color_0");
        public static CharacterEyeColorDescription Color1 = new("color_1");
        public static CharacterEyeColorDescription Color2 = new("color_2");
        public static CharacterEyeColorDescription Color3 = new("color_3");
        public static CharacterEyeColorDescription Color4 = new("color_4");
        public static CharacterEyeColorDescription Color5 = new("color_5");
    }
}