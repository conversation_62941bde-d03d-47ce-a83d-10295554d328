using System.Numerics;
using Models.References.Builder;

namespace Models.References.KinematicObject
{
    public class LocationBreachKinematicObjectsControllerDescription
    {
        public LocationBreachKinematicObjectsControllerDescription(BreachKinematicObjectsControllerEntityIds breachKinematicObjectsControllerEntityIds, int plotKinematicObjectsControllerIndex, int buildingKinematicObjectsControllerIndex, Vector3 position)
        {
            BreachKinematicObjectsControllerEntityIds = breachKinematicObjectsControllerEntityIds;
            PlotKinematicObjectsControllerIndex = plotKinematicObjectsControllerIndex;
            BuildingKinematicObjectsControllerIndex = buildingKinematicObjectsControllerIndex;
            Position = position;
        }
        
        public BreachKinematicObjectsControllerEntityIds BreachKinematicObjectsControllerEntityIds { get; }
        public int PlotKinematicObjectsControllerIndex { get; }
        public int BuildingKinematicObjectsControllerIndex { get; }
        public Vector3 Position { get; }
    }
}