using System.Collections.Generic;
using System.Numerics;
using Models.MathUtils;
using Models.References.Door;
using Models.References.KinematicObject;
using Models.References.Location;
using Models.References.PlotBuildingDefenseBlock;

namespace Models.References.Builder
{
    public record LocationCompoundWallDescription(
        List<Vector3> Switches,
        IReadOnlyDictionary<CompoundWallDoorSlotDescription, LocationBuilderDoorDescription> Doors,
        IReadOnlyDictionary<BreachKinematicObjectsControllerSlotDescription, LocationBreachKinematicObjectsControllerDescription> BreachKinematicObjectsControllers,
        IReadOnlyList<OBB> <PERSON>bbs,
        AABB Aabb,
        LocationPlotBuildingDefenseBlockDescription DefenseBlockDescription
    );
}