using System;
using System.Numerics;
using System.Collections.Generic;
using Framework.Replication.Enum;
using Models.MathUtils;
using Models.Physics.Types;
using Models.References.Builder;
using Models.References.CargoWorkbench;
using Models.References.Collection;
using Models.References.Door;
using Models.References.HangarShelving;
using Models.References.HangarStorage;
using Models.References.KinematicObject;
using Models.References.Location;
using Models.References.Location.LocationTraders;
using Models.References.Location.LocationVehiclesSpawnPoints;
using Models.References.MoneyWorkbench;
using Models.References.Physics;
using Models.References.Plot.Colliders;
using Models.References.Switch;
using Models.References.Trader;
using Models.References.Trigger;
using Models.References.Vehicle;
using Models.References.Builder.Colliders;
using Models.References.Inventory;
using Models.References.Plot;
using Models.HitBox.Types;
using Models.References.PlotContainers;
using Models.References.WindowBlinds;
using Models.Physics.Shapes.Data.Common;
using Models.Physics.Shapes.Data.Hull;
using Models.References.Colliders;
#if UNITY_MATH
using Unity.Collections;
using Unity.Mathematics;
#endif

namespace Models.References.Builder
{
/// <auto-generated>This is autogenerated class from <see cref="PlotMainBuildingSchemeDescriptionFile"/></auto-generated>
public partial class MainBuildingDescription
{
    public static MainBuildingDescription Home2 { get; } = new MainBuildingDescription(
        id: "home_2",
        colliders: MainBuildingHome2Colliders.Main,
        obbs: new OBB[]{ new OBB(new Vector3(-24.0923f, 4.8264f, -0.3998f), new Vector3(13.2717f, 5.8486f, 25.8738f), new Quaternion(0f, 0f, 0f, 1f)), new OBB(new Vector3(0.3924f, 1.5542f, -9.8188f), new Vector3(16.8515f, 3.7481f, 8.5315f), new Quaternion(0f, 0f, 0f, 1f)), new OBB(new Vector3(15.0046f, 2.5136f, 13.0215f), new Vector3(4.3409f, 2.7887f, 7.623f), new Quaternion(0f, 0f, 0f, 1f)), new OBB(new Vector3(-3.1763f, 2.5136f, 16.5925f), new Vector3(4.5144f, 2.7f, 1.5837f), new Quaternion(0f, 0.1486724f, 0f, 0.9888865f)), new OBB(new Vector3(37.2643f, 2.5136f, 24.2533f), new Vector3(0.8831f, 2.7887f, 1.0421f), new Quaternion(0f, 0f, 0f, 1f)), new OBB(new Vector3(39.5216f, 2.5136f, -21.9043f), new Vector3(1.2874f, 2.7887f, 1.1494f), new Quaternion(0f, 0f, 0f, 1f)) },
        inventoryItem: () => InventoryItemDescription.Home2,
        furnitureSlotsInteractPosition: new Vector3(-16.6f, 0.094f, -3.006f),
        copFurnitureSlot: new PlotConstructionSlotDescription(new Vector3(-14.141f, 0.0906f, -2.738f), new Quaternion(0f, -0.004902647f, 0f, 0.999988f)),
        collectionTableSlot: new PlotConstructionSlotDescription(new Vector3(-28.529f, 0.0991f, 7.566f), new Quaternion(0f, 0.7071049f, 0f, 0.7071086f)),
        cargoWorkbenchSlot: new PlotConstructionSlotDescription(new Vector3(-29.049f, 0.0986f, -2.918f), new Quaternion(0f, -6.16908E-06f, 0f, 1f)),
        storageSlots: new Dictionary<StorageSlotDescription, PlotConstructionSlotDescription>()
        {
            {StorageSlotDescription.Slot0, new PlotConstructionSlotDescription(new Vector3(-28.5663f, 0.1427f, 11.333f), new Quaternion(0f, 1f, 0f, 0f))},
            {StorageSlotDescription.Slot1, new PlotConstructionSlotDescription(new Vector3(-27.4464f, 0.1427f, 11.333f), new Quaternion(0f, 1f, 0f, 0f))},
            {StorageSlotDescription.Slot2, new PlotConstructionSlotDescription(new Vector3(-26.2864f, 0.1427f, 11.3327f), new Quaternion(0f, 1f, 0f, 0f))},
            {StorageSlotDescription.Slot3, new PlotConstructionSlotDescription(new Vector3(-25.1083f, 0.1427f, 11.3327f), new Quaternion(0f, 1f, 0f, 0f))},
            {StorageSlotDescription.Slot4, new PlotConstructionSlotDescription(new Vector3(-23.9683f, 0.1427f, 11.3327f), new Quaternion(0f, 1f, 0f, 0f))},
            {StorageSlotDescription.Slot5, new PlotConstructionSlotDescription(new Vector3(-22.7573f, 0.1427f, 11.3327f), new Quaternion(0f, 1f, 0f, 0f))},
        },
        shelvingSlots: new Dictionary<ShelvingSlotDescription, PlotConstructionSlotDescription>()
        {
            {ShelvingSlotDescription.Slot0, new PlotConstructionSlotDescription(new Vector3(-23.2523f, 0.095f, -2.61f), new Quaternion(0f, 1.937151E-07f, 0f, 1f))},
            {ShelvingSlotDescription.Slot1, new PlotConstructionSlotDescription(new Vector3(-28.7864f, 0.095f, 0.808f), new Quaternion(0f, 0.7071069f, 0f, 0.7071066f))},
            {ShelvingSlotDescription.Slot2, new PlotConstructionSlotDescription(new Vector3(-28.7864f, 0.095f, 4.963f), new Quaternion(0f, 0.7071069f, 0f, 0.7071066f))},
            {ShelvingSlotDescription.Slot3, new PlotConstructionSlotDescription(new Vector3(-22.6088f, 0.095f, 6.399f), new Quaternion(0f, -0.7071056f, 0f, 0.7071081f))},
            {ShelvingSlotDescription.Slot4, new PlotConstructionSlotDescription(new Vector3(-28.7864f, 0.095f, 2.881f), new Quaternion(0f, 0.7071069f, 0f, 0.7071066f))},
        },
        npcPosition: new Vector3(-25.2253f, 1.3026f, -11.768f),
        planningBoardPosition: new Vector3(-16.809f, 0.1007f, -17.871f),
        carGarage: new PlotGarageDescription<PlotCarSlotDescription>(new Vector3(-18.416f, 1.6075f, -2.988f), new Dictionary<PlotCarSlotDescription, PlotGarageSlotDescription>{ { PlotCarSlotDescription.Slot0, new PlotGarageSlotDescription(new Vector3(-17.4338f, 0.0986f, 0.4478f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f), 0f) },{ PlotCarSlotDescription.Slot1, new PlotGarageSlotDescription(new Vector3(-17.4339f, 0.0986f, 5.4478f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f), 0f) } }),
        doors: new Dictionary<MainBuildingDoorSlotDescription, PlotDoorDescription>()
        {
            {MainBuildingDoorSlotDescription.Slot0, new PlotDoorDescription(DoorDescription.Slider_Window_Base_01, new Vector3(-29.327f, 5.4831f, 2.137f), new Vector3(-29.3051f, 6.2742f, 2.1011f), new Quaternion(0f, -0.7071047f, 0f, -0.7071089f), new OBB[] { new OBB(new Vector3(-27.218f, 6.1834f, 2.308f), new Vector3(4.3568f, 1.8618f, 3.1994f), new Quaternion(0f, -0.7071047f, 0f, -0.7071089f)) }, 3)},
            {MainBuildingDoorSlotDescription.Slot1, new PlotDoorDescription(DoorDescription.Slider_Window_Base_01, new Vector3(-19.9911f, 5.4824f, -6.0932f), new Vector3(-20.013f, 6.2735f, -6.0573f), new Quaternion(0f, 0.7071089f, 0f, -0.7071047f), new OBB[] {  }, 3)},
            {MainBuildingDoorSlotDescription.Slot2, new PlotDoorDescription(DoorDescription.DoorSingleMetal_01, new Vector3(-12.7233f, 0.0986f, -6.0522f), new Vector3(-12.7233f, 1.4486f, -6.0522f), new Quaternion(0f, 0.7071064f, 0f, 0.7071072f), new OBB[] {  }, 3)},
            {MainBuildingDoorSlotDescription.Slot3, new PlotDoorDescription(DoorDescription.DoorSingleWooden_01, new Vector3(-18.0233f, 0.0986f, -6.0521f), new Vector3(-18.0233f, 1.4486f, -6.0521f), new Quaternion(0f, 0.7071085f, 0f, 0.707105f), new OBB[] {  }, 3)},
            {MainBuildingDoorSlotDescription.Slot4, new PlotDoorDescription(DoorDescription.DoorSingleWooden_01, new Vector3(-20.3839f, 0.0986f, -3.1414f), new Vector3(-20.3839f, 1.4486f, -3.1414f), new Quaternion(0f, 1f, 0f, -2.831221E-06f), new OBB[] {  }, 3)},
            {MainBuildingDoorSlotDescription.Slot5, new PlotDoorDescription(DoorDescription.DoorSingleWooden_01, new Vector3(-25.3839f, 0.0986f, -3.1414f), new Vector3(-25.3839f, 1.4486f, -3.1414f), new Quaternion(0f, 1f, 0f, -2.831221E-06f), new OBB[] {  }, 3)},
            {MainBuildingDoorSlotDescription.Slot6, new PlotDoorDescription(DoorDescription.DoorSingleWooden_01, new Vector3(-22.0732f, 0.0986f, 3.0489f), new Vector3(-22.0732f, 1.4486f, 3.0489f), new Quaternion(0f, 0.7071064f, 0f, 0.7071072f), new OBB[] {  }, 3)},
            {MainBuildingDoorSlotDescription.Slot7, new PlotDoorDescription(DoorDescription.DoorSingleWooden_01, new Vector3(-24.0478f, 4.399f, -3.1629f), new Vector3(-24.0478f, 5.749f, -3.1629f), new Quaternion(0f, 4.023314E-06f, 0f, 1f), new OBB[] {  }, 3)},
            {MainBuildingDoorSlotDescription.Slot8, new PlotDoorDescription(DoorDescription.DoorSingleWooden_01, new Vector3(-26.3203f, 4.3988f, 7.1679f), new Vector3(-26.3203f, 5.7488f, 7.1679f), new Quaternion(0f, 4.023314E-06f, 0f, 1f), new OBB[] {  }, 3)},
            {MainBuildingDoorSlotDescription.Slot9, new PlotDoorDescription(DoorDescription.DoorSingleWooden_01, new Vector3(-24.0478f, 4.3986f, -8.9416f), new Vector3(-24.0478f, 5.7486f, -8.9416f), new Quaternion(0f, 4.023314E-06f, 0f, 1f), new OBB[] {  }, 3)},
        },
        windowBlinds: new Dictionary<MainBuildingWindowBlindsSlotDescription, PlotWindowBlindsDescription>()
        {
            {MainBuildingWindowBlindsSlotDescription.Slot0, new PlotWindowBlindsDescription(new Vector3(-14.65f, 2.8098f, -13.7773f), new OBB[] { new OBB(new Vector3(-15.7986f, 1.8939f, -14.0026f), new Vector3(4.5002f, 1.88f, 1.0917f), new Quaternion(0f, 0.7071078f, 0f, 0.7071059f)) })},
            {MainBuildingWindowBlindsSlotDescription.Slot1, new PlotWindowBlindsDescription(new Vector3(-29.047f, 1.6151f, -12.8739f), new OBB[] { new OBB(new Vector3(-27.7606f, 1.2916f, -12.9414f), new Vector3(2.5f, 1.2877f, 1.2296f), new Quaternion(0f, 0.707103f, 0f, -0.7071106f)) })},
            {MainBuildingWindowBlindsSlotDescription.Slot2, new PlotWindowBlindsDescription(new Vector3(-21.7176f, 7.1089f, -18.584f), new OBB[] { new OBB(new Vector3(-24.0134f, 6.1895f, -17.2485f), new Vector3(2.8209f, 1.8836f, 1.2786f), new Quaternion(0f, 1f, 0f, -1.370907E-06f)) })},
            {MainBuildingWindowBlindsSlotDescription.Slot3, new PlotWindowBlindsDescription(new Vector3(-20.1f, 7.1093f, -13.9273f), new OBB[] { new OBB(new Vector3(-21.1743f, 6.2294f, -13.6029f), new Vector3(3.7006f, 1.8441f, 0.9901f), new Quaternion(0f, 0.7071075f, 0f, 0.7071062f)) })},
            {MainBuildingWindowBlindsSlotDescription.Slot4, new PlotWindowBlindsDescription(new Vector3(-30.8739f, 3.8593f, -5.9773f), new OBB[] { new OBB(new Vector3(-29.9739f, 3.7196f, -6.0895f), new Vector3(1.978f, 1.6243f, 0.8432f), new Quaternion(0f, -0.7071061f, 0f, 0.7071075f)) })},
            {MainBuildingWindowBlindsSlotDescription.Slot5, new PlotWindowBlindsDescription(new Vector3(-29.173f, 5.86f, 2.038f), new OBB[] { new OBB(new Vector3(-27.218f, 6.1834f, 2.308f), new Vector3(4.3568f, 1.8618f, 3.1994f), new Quaternion(0f, -0.7071047f, 0f, -0.7071089f)) })},
            {MainBuildingWindowBlindsSlotDescription.Slot6, new PlotWindowBlindsDescription(new Vector3(-20.1451f, 5.8593f, -5.9942f), new OBB[] { new OBB(new Vector3(-21.0451f, 5.6794f, -5.8416f), new Vector3(0.9271f, 1.1441f, 0.8432f), new Quaternion(7.118679E-16f, -0.7071049f, 1.800278E-15f, -0.7071087f)) })},
        },
        plotContainers: new Dictionary<MainBuildingPlotContainerSlotDescription, PlotContainerLocationDescription>()
        {
            {MainBuildingPlotContainerSlotDescription.Slot0, new PlotContainerLocationDescription(PlotContainerDescription.PlotContainer_01, new Vector3(-17.0739f, 0.3381f, -9.821f), new Quaternion(0f, -3.874301E-07f, 0f, 1f))},
            {MainBuildingPlotContainerSlotDescription.Slot1, new PlotContainerLocationDescription(PlotContainerDescription.PlotContainer_01, new Vector3(-23.2597f, 4.85f, 0.0553f), new Quaternion(0f, 0.7071065f, 0f, 0.7071071f))},
            {MainBuildingPlotContainerSlotDescription.Slot2, new PlotContainerLocationDescription(PlotContainerDescription.PlotContainer_01, new Vector3(-23.2597f, 4.85f, 4.3053f), new Quaternion(0f, 0.7071065f, 0f, 0.7071071f))},
            {MainBuildingPlotContainerSlotDescription.Slot3, new PlotContainerLocationDescription(PlotContainerDescription.PlotContainer_02, new Vector3(-16.9331f, 0.6601f, 15.579f), new Quaternion(0f, 0f, 0f, 1f))},
            {MainBuildingPlotContainerSlotDescription.Slot4, new PlotContainerLocationDescription(PlotContainerDescription.PlotContainer_02, new Vector3(-25.8678f, 0.5417f, -11.2953f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f))},
            {MainBuildingPlotContainerSlotDescription.Slot5, new PlotContainerLocationDescription(PlotContainerDescription.PlotContainer_02, new Vector3(-28.1177f, 0.5499f, -12.9342f), new Quaternion(0f, -0.7071068f, 0f, 0.7071068f))},
            {MainBuildingPlotContainerSlotDescription.Slot6, new PlotContainerLocationDescription(PlotContainerDescription.PlotContainer_02, new Vector3(-27.167f, 4.8576f, -11.4437f), new Quaternion(0f, -0.7071068f, 0f, 0.7071068f))},
        },
        moneyWorkbench: new PlotMoneyWorkbenchDescription(new Vector3(-21.7784f, 0.959f, -17.804f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(0.066f, 0.455f, -0.178f)),
        kinematicObjectsControllers: new KinematicObjectsControllerDescription[]
        {
            new KinematicObjectsControllerDescription(
				new KinematicObjectDescription[] {
						new KinematicObjectDescription(
						new CollidersDescription(
							boxes: new BoxColliderDescription[] {
								new BoxColliderDescription(new Vector3(-1.9501f, 0.145f, -1.4411f), new Quaternion(0f, 1.353002E-14f, 0f, 1f), new Vector3(9.4001f, 3.1101f, 0.1f), 0, SurfaceType.Metal, HitType.Default, 1f, 1f)
							}, 
							triangles: Array.Empty<TriangleColliderDescription>(), 
							capsules: Array.Empty<CapsuleColliderDescription>(), 
							hulls: Array.Empty<HullColliderDescription>(), 
							hullData: Array.Empty<HullShapeData>()))
					}, 
				new KinematicObjectMovementDescription[] {
						new KinematicObjectMovementDescription(new Vector3(-13.5751f, 1.508f, 4.8978f), new Quaternion(0f, -0.7071065f, 0f, 0.7071071f), new Vector3(-14.1751f, 3.608f, 4.8978f), new Quaternion(0.2126312f, -0.6743795f, 0.212631f, 0.67438f))
					}, 
				3000, 500, KinematicObjectMovementStateDescription.Point1, false, 
				new OBB[] { new OBB(new Vector3(-13.0046f, 1.0519f, 2.9733f), new Vector3(5.5764f, 3.5439f, 3.5705f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f)) }, 
				new OBB[] { new OBB(new Vector3(-4.4488f, 2.2588f, 5.3183f), new Vector3(10.5521f, 2.8285f, 14.9852f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f)) }, 
				new HashSet<KinematicObjectMovementStateDescription> { KinematicObjectMovementStateDescription.Point1 }),
            new KinematicObjectsControllerDescription(
				new KinematicObjectDescription[] {
						new KinematicObjectDescription(
						new CollidersDescription(
							boxes: new BoxColliderDescription[] {
								new BoxColliderDescription(new Vector3(0f, 0.1453f, -1.4411f), new Quaternion(0f, -7.751375E-15f, 0f, 1f), new Vector3(5.5f, 3.1094f, 0.1f), 0, SurfaceType.Metal, HitType.Default, 1f, 1f)
							}, 
							triangles: Array.Empty<TriangleColliderDescription>(), 
							capsules: Array.Empty<CapsuleColliderDescription>(), 
							hulls: Array.Empty<HullColliderDescription>(), 
							hullData: Array.Empty<HullShapeData>()))
					}, 
				new KinematicObjectMovementDescription[] {
						new KinematicObjectMovementDescription(new Vector3(-13.575f, 1.508f, 12.1479f), new Quaternion(0f, -0.7071065f, 0f, 0.7071071f), new Vector3(-14.175f, 3.608f, 12.1479f), new Quaternion(0.2126312f, -0.6743795f, 0.212631f, 0.67438f))
					}, 
				3000, 500, KinematicObjectMovementStateDescription.Point1, false, 
				new OBB[] { new OBB(new Vector3(-12.325f, 0.6757f, 12.147f), new Vector3(3.0008f, 3.1677f, 3f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f)) }, 
				new OBB[] { new OBB(new Vector3(-4.2419f, 4.2175f, 10.8814f), new Vector3(9.2833f, 4.5378f, 15.1921f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f)) }, 
				new HashSet<KinematicObjectMovementStateDescription> { KinematicObjectMovementStateDescription.Point1 }),
            new KinematicObjectsControllerDescription(
				new KinematicObjectDescription[] {
						new KinematicObjectDescription(
						new CollidersDescription(
							boxes: new BoxColliderDescription[] {
								new BoxColliderDescription(new Vector3(-0.8721f, 0.3382f, -2.6749f), new Quaternion(0f, 1.938109E-14f, 0f, 1f), new Vector3(0.6422f, 0.2233f, 0.0778f), RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f),
								new BoxColliderDescription(new Vector3(0.8811f, 0.3382f, -2.6749f), new Quaternion(0f, 2.766124E-14f, 0f, 1f), new Vector3(0.6504f, 0.2233f, 0.0778f), RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f),
								new BoxColliderDescription(new Vector3(-0.8785f, 0.1282f, -0.103f), new Quaternion(0f, 1.097626E-14f, 0f, 1f), new Vector3(0.6576f, 0.1959f, 5.0543f), RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f),
								new BoxColliderDescription(new Vector3(0.8716f, 0.1282f, -0.103f), new Quaternion(0f, 5.233751E-15f, 0f, 1f), new Vector3(0.6348f, 0.1959f, 5.0543f), RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f),
								new BoxColliderDescription(new Vector3(-0.0036f, 0.1321f, -2.6034f), new Quaternion(0f, 8.415936E-15f, 0f, 1f), new Vector3(3.3478f, 0.1913f, 0.1671f), RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f),
								new BoxColliderDescription(new Vector3(-0.0036f, 0.1321f, 2.4f), new Quaternion(0f, 8.415936E-15f, 0f, 1f), new Vector3(3.3478f, 0.1913f, 0.1671f), RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f)
							}, 
							triangles: Array.Empty<TriangleColliderDescription>(), 
							capsules: Array.Empty<CapsuleColliderDescription>(), 
							hulls: Array.Empty<HullColliderDescription>(), 
							hullData: Array.Empty<HullShapeData>()))
					}, 
				new KinematicObjectMovementDescription[] {
						new KinematicObjectMovementDescription(new Vector3(-17.0819f, 0.0986f, 12.2629f), new Quaternion(0f, 0.7071069f, 0f, 0.7071067f), new Vector3(-17.0819f, 1.9986f, 12.2629f), new Quaternion(0f, 0.7071069f, 0f, 0.7071067f))
					}, 
				3000, 0, KinematicObjectMovementStateDescription.Point1, false, 
				new OBB[] {  }, 
				new OBB[] {  }, 
				new HashSet<KinematicObjectMovementStateDescription> { KinematicObjectMovementStateDescription.Point1 }),
        },
        switches: new SwitchDescription[]
        {
            new SwitchDescription(9f, new Vector3(-12.2091f, 1.5031f, 4.7729f), null),
            new SwitchDescription(9f, new Vector3(-11.8921f, 1.5031f, 1.1178f), null),
            new SwitchDescription(5f, new Vector3(-12.2091f, 1.5031f, 12.023f), null),
            new SwitchDescription(5f, new Vector3(-11.892f, 1.5031f, 12.2719f), null),
            new SwitchDescription(3f, new Vector3(-19.6714f, 1.6395f, 10.3308f), null),
        },
        triggers: new TriggerDescription[]
        {
            new TriggerDescription(new TriggerEventDescription.SwitchTriggered(0), new TriggerConditionDescription.KinematicObjectsControllerNotBreachedState(0), new TriggerActionDescription.SetKinematicObjectsControllerChangeStateTimer(0, 500, false)),
            new TriggerDescription(new TriggerEventDescription.SwitchTriggered(1), new TriggerConditionDescription.KinematicObjectsControllerNotBreachedState(0), new TriggerActionDescription.SetKinematicObjectsControllerChangeStateTimer(0, 500, false)),
            new TriggerDescription(new TriggerEventDescription.SwitchTriggered(2), new TriggerConditionDescription.KinematicObjectsControllerNotBreachedState(1), new TriggerActionDescription.SetKinematicObjectsControllerChangeStateTimer(1, 500, false)),
            new TriggerDescription(new TriggerEventDescription.SwitchTriggered(3), new TriggerConditionDescription.KinematicObjectsControllerNotBreachedState(1), new TriggerActionDescription.SetKinematicObjectsControllerChangeStateTimer(1, 500, false)),
            new TriggerDescription(new TriggerEventDescription.SwitchTriggered(4), new TriggerConditionDescription.NoCondition(), new TriggerActionDescription.SetKinematicObjectsControllerChangeStateTimer(2, 200, false)),
        },
        breachKinematicObjectsControllers: new Dictionary<BreachKinematicObjectsControllerSlotDescription, PlotBreachKinematicObjectsControllerDescription>() 
        {
            {BreachKinematicObjectsControllerSlotDescription.Slot0, new PlotBreachKinematicObjectsControllerDescription(0, new Vector3(-13.1951f, 1.5828f, 4.4611f), 5000)},
            {BreachKinematicObjectsControllerSlotDescription.Slot1, new PlotBreachKinematicObjectsControllerDescription(1, new Vector3(-13.1951f, 1.5829f, 11.7112f), 5000)},
        },
        terrainCarveZones: new TerrainCarveZoneDescription[]
        {
            new TerrainCarveZoneDescription(new Vector2(1.159118533f, -11.913007736f), new Vector2(0.999997616f, -0.002177133f), new Vector2(0.002177133f, 0.999997616f), new Vector2(12.76061821f, 11.620250702f)),
        },
        spawnPoint: new OrientedPoint(new Vector3(-27.5f, 4.4079f, 2.5f), new Quaternion(0f, 0.9985958f, 0f, 0.05297538f)),
        defenseSettings: PlotBuildingDefenseSettingsDescription.Home2,
        defenseInteractionPoint: new Vector3(-13.198f, 1.735f, 16.779f)
    );
}
}
