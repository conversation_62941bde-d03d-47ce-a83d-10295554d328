using System.Numerics;
using ClientCore.Prediction;
using Framework.Ecs.System;
using Framework.Ecs.Tick;
using Models.Models.PhysicsSceneModel;
using Models.Models.TickDataModels.Helicopter;
using Models.Models.TickDataModels.Vehicle.Wheel;
using Models.Physics.PhysicsBodies;
using Models.Timeline.Data.Entity;
using Models.Timeline.Data.Internal;
using Models.Utils.Timeline.Data;

namespace ClientCore.Systems
{
    public class HelicopterAnyPredictionUpdatePhysicsAndTickDataSystem : System<(PhysicsBodyId bodyId, TimelineSnapshotsData<HelicopterTimelineData> snapshots, TimelineExtrapolationsData<HelicopterTimelineData> extrapolations, HelicopterEngineTickDataModel engineTickData, VehicleWheelSuspensionTickDataModel[] wheels)>
    {
        private readonly IPhysicsSceneModel _physicsSceneModel;

        private const float _eps = .00000001f;
    
        public HelicopterAnyPredictionUpdatePhysicsAndTickDataSystem(IPhysicsSceneModel physicsSceneModel)
        {
            _physicsSceneModel = physicsSceneModel;
        }
    
        protected override void Tick(ITick tick)
        {
            long tickIndex = tick.TickIndex;
            long prevTickIndex = tickIndex - 1;

            for (int i = 0; i < _count; i++)
            {
                ref var component = ref _components[i];
                var bodyId = component.bodyId;
                var snapshots = component.snapshots;
                var extrapolations = component.extrapolations;
                var engineTickData = component.engineTickData;
                var wheels = component.wheels;

                PredictionSimulationTimeline.GetLatestStateForTickIndex(snapshots, extrapolations, prevTickIndex, HelicopterTimelineDataExtensions.InterpolationFunc, out HelicopterTimelineData timelineData);
            
                UpdatePhysicsScene(timelineData, bodyId);

                engineTickData.EngineActive = timelineData.EngineActive;
                engineTickData.EngineStateChangeOverTs = timelineData.EngineStateChangeOverTs;

                wheels[0].SpringLength = timelineData.FLSpringLength;
                wheels[1].SpringLength = timelineData.FRSpringLength;
                wheels[2].SpringLength = timelineData.BLSpringLength;
                wheels[3].SpringLength = timelineData.BRSpringLength;
            }
        }
    
        private void UpdatePhysicsScene(in HelicopterTimelineData timelineData, PhysicsBodyId bodyId)
        {
            Vector3 timelineCenterOfMassPosition = timelineData.CenterOfMassPosition;
            Quaternion timelineCenterOfMassOrientation = timelineData.CenterOfMassOrientation;
        
            Vector3 bodyCenterOfMassPosition = _physicsSceneModel.GetBodyPosition(bodyId);
            Quaternion bodyCenterOfMassOrientation = _physicsSceneModel.GetBodyOrientation(bodyId);

            bool isPositionNeedUpdate = (timelineCenterOfMassPosition - bodyCenterOfMassPosition).LengthSquared() > _eps;
        
            Vector4 bodyCenterOfMassOrientationVector = new Vector4(bodyCenterOfMassOrientation.X, bodyCenterOfMassOrientation.Y, bodyCenterOfMassOrientation.Z, bodyCenterOfMassOrientation.W);
            Vector4 timelineCenterOfMassOrientationVector = new Vector4(timelineCenterOfMassOrientation.X, timelineCenterOfMassOrientation.Y, timelineCenterOfMassOrientation.Z, timelineCenterOfMassOrientation.W);
            bool isOrientationNeedUpdate = (bodyCenterOfMassOrientationVector - timelineCenterOfMassOrientationVector).LengthSquared() > _eps;
        
            bool needWarp = isPositionNeedUpdate || isOrientationNeedUpdate;
            if (needWarp)
            {
                _physicsSceneModel.Warp(bodyId, timelineCenterOfMassPosition, timelineCenterOfMassOrientation);
            }

            Vector3 bodyLinearVelocity = _physicsSceneModel.GetBodyLinearVelocity(bodyId);
            Vector3 bodyAngularVelocity = _physicsSceneModel.GetBodyAngularVelocity(bodyId);

            Vector3 timelineLinearVelocity = timelineData.LinearVelocity;
            Vector3 timelineAngularVelocity = timelineData.AngularVelocity;
        
            bool isLinearVelocityNeedUpdate = (bodyLinearVelocity - timelineLinearVelocity).LengthSquared() > _eps;
            if (isLinearVelocityNeedUpdate)
            {
                _physicsSceneModel.SetLinearVelocity(bodyId, timelineLinearVelocity);
            }
        
            bool isAngularVelocityNeedUpdate = (bodyAngularVelocity - timelineAngularVelocity).LengthSquared() > _eps;
            if (isAngularVelocityNeedUpdate)
            {
                _physicsSceneModel.SetAngularVelocity(bodyId, timelineAngularVelocity);
            }
        
            _physicsSceneModel.SetSleepCounter(bodyId, timelineData.SleepCounter);
        }
    }
}