using System;
using System.Collections.Generic;

namespace ClientCore.ReplicationDebug
{
    public static class Percentile
    {
        public static T[] Calculate<T>(IEnumerable<T> sequence, float[] percentiles)
        {
            var percentilesLength = percentiles.Length;

            if (percentilesLength == 0)
                throw new Exception("Percentiles is empty!");

            var sortedSequence = new List<T>(sequence);
            sortedSequence.Sort();

            var sequenceLength = sortedSequence.Count;
            if (sequenceLength == 0)
                throw new Exception("Sequence is empty!");

            var result = new T[percentilesLength];

            for (int i = 0; i < percentilesLength; i++)
            {
                var percentile = percentiles[i];
                var percentileSequenceValueIndex = Math.Clamp((int)(sequenceLength * percentile), 0, sequenceLength - 1);
                var percentileSequenceValue = sortedSequence[percentileSequenceValueIndex];

                result[i] = percentileSequenceValue;
            }

            return result;
        }
    }
}