using Framework.Analytics.AnalyticsRequest;
using Framework.Utility.Csv;
using Models.References.Clan;
using Models.References.ClanBattle;

namespace Server.Clan.Analytics.Requests
{
    public readonly struct RemoveClanObjectAuctionBetAnalyticsRequest : IAnalyticsRequest
    {
        public string Tag => "remove_clan_object_auction_bet";
        public string ClanId { get; init; }
        public ClanRankDescription RankDescription { get; init; }
        public ClanConquestObjectTypeDescription ConquestObjectTypeDescription { get; init; }
        public int Amount { get; init; }
        public int RealmId { get; init; }
        public long RealmStartTs { get; init; }
    
        public void Write(ICsv csv)
        {
            csv.Add(ClanId);
            csv.Add(RankDescription.Id);
            csv.Add(ConquestObjectTypeDescription.Id);
            csv.Add(Amount);
            csv.Add(RealmId);
            csv.Add(RealmStartTs);
        }
    }
}