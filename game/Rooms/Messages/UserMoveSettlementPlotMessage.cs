using Framework.Replication.Data;
using Framework.Replication.Data.Primitive;
using Models.References;
using Models.References.Realm;

namespace Server.Messages
{
    public class UserMoveSettlementPlotMessage : CompositeData
    {
        private readonly IPrimitiveData<SectorDescription> _sector = new ReferenceData<SectorDescription>(SectorDescription.Enum);
        private readonly IPrimitiveData<SettlementDescription> _settlement = new ReferenceData<SettlementDescription>(SettlementDescription.Enum);
        private readonly IPrimitiveData<int> _spotIndex = new IntData(0, PlotSpotDescription.MaxSpotsCount);
    
        public UserMoveSettlementPlotMessage(SectorDescription sector, SettlementDescription settlement, int spotIndex) : this()
        {
            _sector.Value = sector;
            _settlement.Value = settlement;
            _spotIndex.Value = spotIndex;
        }

        public UserMoveSettlementPlotMessage()
        {
            Add("sector", _sector);
            Add("settlement", _settlement);
            Add("spot_index", _spotIndex);
        }

        public SectorDescription Sector => _sector.Value;
        public SettlementDescription Settlement => _settlement.Value;
        public int SpotIndex => _spotIndex.Value;
    }
}