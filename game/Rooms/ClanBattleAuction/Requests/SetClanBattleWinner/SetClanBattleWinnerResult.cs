using System;
using Framework.Core.BinaryStream;
using Framework.Ecs.Serializer;

namespace Server.ClanBattleAuction.Requests.SetClanBattleWinner
{
    public abstract record SetClanBattleWinnerResult
    {
        public record AuctionNotReadyError : SetClanBattleWinnerResult;
        public record UnknownClanError : SetClanBattleWinnerResult;
        public record WaitingSaveError : SetClanBattleWinnerResult;
        public record Ok : SetClanBattleWinnerResult;
    }

    public class SetClanBattleWinnerResultSerializer : ISerializer<SetClanBattleWinnerResult>
    {
        public void Put(SetClanBattleWinnerResult value, IBinaryWriteStream stream)
        {
            switch (value)
            {
                case SetClanBattleWinnerResult.AuctionNotReadyError:
                    stream.WriteByte(0);
                    break;
                case SetClanBattleWinnerResult.UnknownClanError:
                    stream.WriteByte(1);
                    break;
                case SetClanBattleWinnerResult.WaitingSaveError:
                    stream.WriteByte(2);
                    break;
                case SetClanBattleWinnerResult.Ok:
                    stream.WriteByte(3);
                    break;
                default:
                    throw new ArgumentOutOfRangeException(nameof(value));
            }
        }

        public bool TryGet(IBinaryReadStream stream, out SetClanBattleWinnerResult value)
        {
            if (stream.TryReadByte(out var index))
            {
                switch (index)
                {
                    case 0:
                        value = new SetClanBattleWinnerResult.AuctionNotReadyError();
                        return true;
                    case 1:
                        value = new SetClanBattleWinnerResult.UnknownClanError();
                        return true;
                    case 2:
                        value = new SetClanBattleWinnerResult.WaitingSaveError();
                        return true;
                    case 3:
                        value = new SetClanBattleWinnerResult.Ok();
                        return true;
                }
            }

            value = default;
            return false;
        }
    }
}