using System;
using Framework.Async.Awaiter;
using Framework.Core.Either;
using Framework.Value.Values;
using Models.Models.BattleCharacterModel;
using Models.Models.OwnedItemsModel;
using Models.Models.WorldEntitiesModel;
using Models.Utils;
using Server.Battle.Models.States;
using Server.Common.Utils;

namespace Server.Battle.Controllers.BattleOwnedItemsController
{
    public class BattleOwnedItemsController<T> : IBattleOwnedItemsController<T>
    {
        private readonly IEither<IValue, IValue> _validationError = ServerResponses.Validation.ToEitherError();
    
        private readonly BattleRoomState _state;
        private readonly IWorldEntitiesModel _worldEntitiesModel;
        private readonly Func<IBattleCharacterModel, IOwnedItemsModel<T>> _getOwnedItemsFunc;
        private readonly Func<T, bool> _canBuyItemFunc;
        private readonly Func<T, (int, int)> _getItemBuyParamsFunc;

        public BattleOwnedItemsController(BattleRoomState state, IWorldEntitiesModel worldEntitiesModel, Func<IBattleCharacterModel, IOwnedItemsModel<T>> getOwnedItemsFunc, Func<T, bool> canBuyItemFunc, Func<T, (int, int)> getItemBuyParamsFunc)
        {
            _state = state;
            _worldEntitiesModel = worldEntitiesModel;
            _getOwnedItemsFunc = getOwnedItemsFunc;
            _canBuyItemFunc = canBuyItemFunc;
            _getItemBuyParamsFunc = getItemBuyParamsFunc;
        }
    
        public async IAwaiter<IEither<IValue, IValue>> Buy(string privateId, T item)
        {
            if (!TryGetBattleCharacter(privateId, out var battleCharacterModel))
            {
                return _validationError;
            }

            var characterValidationModel = battleCharacterModel.ValidationModel;
            if (!characterValidationModel.CanPerformIdleActions() && !characterValidationModel.CanPerformInVehicleActions())
            {
                return _validationError;
            }

            var ownedItems = _getOwnedItemsFunc(battleCharacterModel);

            if (item == null || !_canBuyItemFunc(item) || ownedItems.Contains(item))
            {
                return _validationError;
            }

            var (price, yearsRequirement) = _getItemBuyParamsFunc(item);

            var imprisonmentYears = battleCharacterModel.PlayerBattleModel.ImprisonmentYears;
            var cryptoCoinsModel = battleCharacterModel.PlayerBattleModel.CryptoCoinsModel;

            if (price > cryptoCoinsModel.Value || yearsRequirement > imprisonmentYears.Value)
            {
                return _validationError;
            }
        
            cryptoCoinsModel.Dec(price);
            ownedItems.Add(item);
        
            return AsyncResponses.OkEither;
        }

        private bool TryGetBattleCharacter(string privateId, out IBattleCharacterModel model)
        {
            if (_state.PlayerStates.TryGetValue(privateId, out var state))
            {
                model = _worldEntitiesModel.BattleCharacters[state.EntityId];
                return true;
            }

            model = null;
            return false;
        }
    }
}