using System;
using System.Net;
using Framework.Btl.Receipt;
using Framework.Cluster.Link;
using Framework.Cluster.Message;
using Framework.Cluster.Record;
using Framework.Cluster.RoomContext;
using Framework.Cluster.RoomHandlers;
using Framework.Async.Awaiter;
using Framework.Core.Either;
using Framework.Core.Unit;
using Framework.Value.Values;
using Framework.Ecs.Handlers;
using Framework.Ecs.Message;
using Framework.Ecs.Tick;
using Framework.Metrics.Metric;
using Framework.Replication.Serializer.Value.Read;
using Framework.Replication.Serializer.Value.Write;
using Models.Data.Hangar;
using Models.Models.LocationPlotSpotsModel;
using Models.Models.LocationStaticGeometry;
using Models.References.Location.LocationStaticGeometry;
using Server.Battle.Controllers.BattleInfoController;
using Server.Battle.Controllers.BattleJoinSettlementController;
using Server.Battle.Controllers.BattlePlotOwnerClanController;
using Server.Battle.Controllers.BattleReserveSettlementSlotController;
using Server.Battle.Controllers.BattleSetSettlementPlotController;
using Server.Battle.Controllers.BattleSettlementCollectionController;
using Server.Battle.Controllers.BattleSettlementOwnerController;
using Server.Battle.Controllers.LocationPlotSpotsController;
using Server.Battle.Controllers.StopBattle;
using Server.Battle.Data;
using Server.Battle.Destination;
using Server.Battle.Initialize.BattleInitController;
using Server.Battle.InitializedBattle;
using Server.Battle.Metrics;
using Server.Battle.Metrics.Labels;
using Server.Battle.Models.BattleContext;
using Server.Battle.Models.HangarSyncModel;
using Server.Battle.Models.SettlementRoomModel;
using Server.Battle.Models.States;
using Server.Battle.Requests;
using Server.Common.Controllers.LoadController;
using Server.Common.Controllers.RoomStopController;
using Server.Common.Controllers.SaveController;
using Server.Common.CooldownByAttempt;
using Server.Common.Models.SaveDelayModel;
using Server.Common.Utils;
using Server.References;
using File = Framework.Core.File.File;
using SynchronizationContext = Framework.Core.SynchronizationContext.SynchronizationContext;

namespace Server.Battle.Room
{
    public record BattleRoomDescription(long RoomTimeoutTime, int RestartRoomTimeoutTime, long DisconnectedPeerTimeoutTime, int DisconnectedPeerRestartTimeoutTime, long WaitingInitTimeoutTime, ICooldownByAttempt CooldownByAttempt, DatabaseDescription DatabaseDescription, BattleInitTickScore InitTickScore, string ContentDirectoryPath);

    public class BattleRoom : Framework.Cluster.Room.Room
    {
        public static readonly RoomHandlers<BattleRoom> Handlers = new(
            RegularHandlers: new RequestHandlers<BattleRoleDestination, BattleRoom>(BattleRequests.Requests)
            {
                { BattleRequests.JoinSettlement, (room, input) => room._state.LoadState.HandleAfterLoad(input, room._joinSettlementController.JoinSettlement) },
                { BattleRequests.GetSettlementCollection, (room, input) => room._state.LoadState.HandleAfterLoad(input, room._settlementCollectionController.Get) },
                { BattleRequests.PlotOwnerClanChanged, (room, input) => room._state.LoadState.HandleAfterLoad(input, room._plotOwnerClanController.Change) },
                { BattleRequests.ReserveSettlementSlot, (room, input) => room._state.LoadState.HandleAfterLoad(input, room._reserveSettlementSlotController.ReserveSlot) },
                { BattleRequests.SetSettlementPlot, (room, input) => room._state.LoadState.HandleAfterLoad(input, room._setSettlementPlotController.Set) },
                { BattleRequests.CheckSettlementPlotSet, (room, input) => room._state.LoadState.HandleAfterLoad(input, room._setSettlementPlotController.CheckSet) },
                { BattleRequests.SetSettlementOwner, (room, input) => room._state.LoadState.HandleAfterLoad(input, room._settlementOwnerController.SetOwner) },
                { BattleRequests.UnsetSettlementOwner, (room, input) => room._state.LoadState.HandleAfterLoad(input, room._settlementOwnerController.UnsetOwner) },
                { BattleRequests.ClanBonusChanged, (room, input) => room._state.LoadState.HandleAfterLoad(input, room._initializedBattle.ClanBonusController.ClanBonusChanged) },
                { BattleRequests.AdminGetRoomData, (room, input) => room._state.LoadState.HandleAfterLoad(room._initializedBattle.AdminController.GetRoomData) },
                { BattleRequests.AdminPeerMessage, (room, input) => room._state.LoadState.HandleAfterLoad(input, room._initializedBattle.AdminPeerMessageController.Process) },
            },
            LinkHandlers: new RequestHandlers<BattleRoleDestination, (BattleRoom Room, string SenderRole, string SenderRoom)>(BattleRequests.Requests)
            {
                { BattleRequests.InitBattle, (args, input) => args.Room._state.LoadState.HandleAfterLoad(input, args.SenderRole, args.SenderRoom, args.Room._initController.InitBattle) },
                { BattleRequests.ReserveSettlementSlot, (args, input) => args.Room._state.LoadState.HandleAfterLoad(input, args.Room._reserveSettlementSlotController.ReserveSlot) },
                { BattleRequests.GetJoinBattleDataForObserver, (args, input) => args.Room._state.LoadState.HandleAfterLoad(input, args.Room._battleInfoController.GetJoinDataForObserver) },
                { BattleRequests.JoinHangarBattle, (args, input) => args.Room._state.LoadState.HandleAfterLoad(input, args.Room._initializedBattle.RoomJoinController.JoinHangarBattle) },
                { BattleRequests.JoinShooterBattle, (args, input) => args.Room._state.LoadState.HandleAfterLoad(input, args.Room._initializedBattle.RoomJoinController.JoinShooterBattle) },
                { BattleRequests.HoldBattleSlot, (args, input) => args.Room._state.LoadState.HandleAfterLoad(input, args.Room._initializedBattle.SlotsRequestsController.MatchmakingReserveSlot) },
                { BattleRequests.ForceLeaveBattle, (args, input) => args.Room._state.LoadState.HandleAfterLoad(input, args.Room._initializedBattle.ForceLeaveController.ForceLeaveBattle) },
                { BattleRequests.PartyChanged, (args, input) => args.Room._state.LoadState.HandleAfterLoad(input, args.Room._initializedBattle.PartyController.PartyChanged) },
                { BattleRequests.LeaveSettlement, (args, input) => args.Room._state.LoadState.HandleAfterLoad(input, args.Room._initializedBattle.LeaveSettlementController.LeaveSettlement) },
                { BattleRequests.HangarBattleReserveSlots, (args, input) => args.Room._state.LoadState.HandleAfterLoad(input, args.Room._initializedBattle.SlotsRequestsController.SettlementReserveSlots) },
                { BattleRequests.ReserveSlots, (args, input) => args.Room._state.LoadState.HandleAfterLoad(input, args.Room._initializedBattle.SlotsRequestsController.CheatShooterReserveSlot) },
                { BattleRequests.ClanBattleReserveSlots, (args, input) => args.Room._state.LoadState.HandleAfterLoad(input, args.Room._initializedBattle.SlotsRequestsController.ClanBattleReserveSlot) },
                { BattleRequests.AdminAddCoins, (args, input) => args.Room._state.LoadState.HandleAfterLoad(input, args.Room._initializedBattle.LayerCurrencyController.AdminAddCoins) },
                { BattleRequests.TransferCoinsFromBattle, (args, input) => args.Room._state.LoadState.HandleAfterLoad(input, args.SenderRoom, args.Room._initializedBattle.CoinsTransactionsController.TransferCoinsFromBattle) },
                { BattleRequests.TransferCoinsToBattle, (args, input) => args.Room._state.LoadState.HandleAfterLoad(input, args.SenderRoom, args.Room._initializedBattle.CoinsTransactionsController.TransferCoinsToBattle) },
                { BattleRequests.ClanChanged, (args, input) => args.Room._state.LoadState.HandleAfterLoad(input, args.SenderRoom, args.Room._initializedBattle.CharacterClanChangedController.SetClan) },
                { BattleRequests.JoinClanBattle, (args, input) => args.Room._state.LoadState.HandleAfterLoad(input, args.Room._initializedBattle.RoomJoinController.JoinClanBattle) },
                { BattleRequests.ClanRankChanged, (args, input) => args.Room._state.LoadState.HandleAfterLoad(input, args.SenderRoom, args.Room._initializedBattle.ClanRankController.ClanRankChanged) },
                { BattleRequests.ClanBonusChanged, (args, input) => args.Room._state.LoadState.HandleAfterLoad(input, args.Room._initializedBattle.ClanBonusController.ClanBonusChanged) },
                { BattleRequests.StartMoveSettlementPlot, (args, input) => args.Room._state.LoadState.HandleAfterLoad(input, args.Room._initializedBattle.MoveSettlementPlotController.StartMove) },
                { BattleRequests.FinishMoveSettlementPlot, (args, input) => args.Room._state.LoadState.HandleAfterLoad(input, args.Room._initializedBattle.MoveSettlementPlotController.FinishMove) },
                { BattleRequests.JoinBattleAsObserver, (args, input) => args.Room._state.LoadState.HandleAfterLoad(input, args.Room._initializedBattle.RoomJoinController.JoinBattleAsObserver) },
                { BattleRequests.PartyMembersChanged, (args, input) => args.Room._state.LoadState.HandleAfterLoad(input, args.Room._initializedBattle.PartyController.PartyMemberChanged) },
                { BattleRequests.BuyCryptoCoinsPack, (args, input) => args.Room._state.LoadState.HandleAfterLoad(input, args.SenderRoom, args.Room._initializedBattle.CryptoCoinsPackController.Buy) },
                { BattleRequests.ResetPlotVehicle, (args, input) => args.Room._state.LoadState.HandleAfterLoad(input, args.SenderRoom, args.Room._initializedBattle.ResetPlotVehicleController.ResetPlotVehicle) },
                { BattleRequests.ReleasePartyVehicle, (args, input) => args.Room._state.LoadState.HandleAfterLoad(input, args.SenderRoom, args.Room._initializedBattle.PartyVehicleController.ReleaseVehicle) },
                { BattleRequests.CheckJoinClanBattleAppointment, (args, input) => args.Room._state.LoadState.HandleAfterLoad(input, args.SenderRoom, args.Room._initializedBattle.CheckJoinClanBattleAppointmentController.Check) },
                { BattleRequests.JoinPrisonBattle, (args, input) => args.Room._state.LoadState.HandleAfterLoad(input, args.Room._initializedBattle.RoomJoinController.JoinPrisonBattle) },
                { BattleRequests.GetSettlementAssetsValue, (args, input) => args.Room._state.LoadState.HandleAfterLoad(input, args.Room._initializedBattle.GetSettlementAssetsValueController.Get) },
                { BattleRequests.UpdateSectorsOwners, (args, input) => args.Room._state.LoadState.HandleAfterLoad(input, args.Room._initializedBattle.SectorsOwnersController.UpdateSectorOwners) },
            }
        );

        private readonly IAwaiter<IEither<IValue, IValue>> _loadingReasonAwaiter = "loading".ToAwaiterError();
        private readonly IValue _nullValue = new NullValue();

        private readonly Tick _tick;
        private readonly BattleRoomState _state = new();
        private readonly SettlementRoomData _settlementRoomData;
        private readonly HangarData _hangarData;
        private readonly ISettlementRoomModel _settlementRoomModel;
        private readonly IHangarSyncModel _hangarSyncModel = new HangarSyncModel();
        private readonly SaveController _saveController;
        private readonly Random _random = new Random();
        private readonly IRoomStopController _stopController;
        private readonly IBattleInitController _initController;
        private readonly IBattleJoinSettlementController _joinSettlementController;
        private readonly IBattleSettlementCollectionController _settlementCollectionController;
        private readonly IBattlePlotOwnerClanController _plotOwnerClanController;
        private readonly ILocationPlotSpotsModel _locationPlotSpotsModel;
        private readonly IBattleReserveSettlementSlotController _reserveSettlementSlotController;
        private readonly IBattleSetSettlementPlotController _setSettlementPlotController;
        private readonly IBattleSettlementOwnerController _settlementOwnerController;
        private readonly BattleInfoController _battleInfoController;
        private readonly SynchronizationContext _synchronizationContext;
        
        private IInitializedBattle _initializedBattle;
        private IGauge _gauge;

        public BattleRoom(BattleRoomDescription description, RoomContext context) : base(context)
        {
            var now = context.Now;
            _tick = new Tick((long)now.Get, 0, context.TickPeriod);

            var logger = context.Loggers.Build(nameof(BattleRoom));

            var hangarData = _hangarData = new HangarData();
            var settlementRoomData = _settlementRoomData = new SettlementRoomData(hangarData.CommonPartData, hangarData.Slots);
            var settlementRoomModel = _settlementRoomModel = new SettlementRoomModel(settlementRoomData, hangarData);

            ILocationPlotSpotsController locationPlotSpotsController = null;

            var databaseDescription = description.DatabaseDescription;
            if (databaseDescription != null)
            {
                var writeSerializer = new WriteValueSerializer();
                var readSerializer = new ReadValueSerializer();

                var regularRecord = new RegularRecord(context.Database, databaseDescription.Dataset.Id, _context.Id, _nullValue, null);
                var periodicMetricRecord = new MetricRecord<byte[]>(regularRecord, databaseDescription.Dataset.Id, context.Metrics);
                var attemptsRecord = new AttemptsRecord<byte[]>(periodicMetricRecord, databaseDescription.MaxAttemptsCount, databaseDescription.AttemptCooldown, context.Timers, logger);
                var formatRecord = new FormatRecord(attemptsRecord, databaseDescription.Dataset.Format);

                var saveDelayModel = new RestartSaveDelayModel(databaseDescription.SaveDelay, databaseDescription.SaveDelayRestartTimeAddition, context.RestartTime, context.Timers, _random);
                _saveController = new SaveController(settlementRoomData, writeSerializer, formatRecord, context.Timers, saveDelayModel);

                var loadController = new LoadController(Init, Start, settlementRoomData, _state.LoadState, formatRecord, readSerializer);
                loadController.Load();

                _locationPlotSpotsModel = new LocationPlotSpotsModel(settlementRoomModel.SpotsPresetIndex);
                locationPlotSpotsController = new LocationPlotSpotsController(settlementRoomModel, _locationPlotSpotsModel);

                _joinSettlementController = new BattleJoinSettlementController(_state, settlementRoomModel.PlotsModel, _saveController, _hangarSyncModel, _locationPlotSpotsModel, locationPlotSpotsController, settlementRoomModel.PlotsToSpawnModel);
                _settlementCollectionController = new BattleSettlementCollectionController(_settlementRoomData);
                _plotOwnerClanController = new BattlePlotOwnerClanController(settlementRoomModel.PlotsModel, _saveController);
                _reserveSettlementSlotController = new BattleReserveSettlementSlotController(_state, settlementRoomModel.PlotsModel, _hangarSyncModel, _locationPlotSpotsModel, locationPlotSpotsController, _saveController);
                _setSettlementPlotController = new BattleSetSettlementPlotController(_state, settlementRoomModel, _hangarSyncModel, _saveController, settlementRoomModel.PlotsToSpawnModel);
                _settlementOwnerController = new BattleSettlementOwnerController(settlementRoomModel.SettlementOwnerModel, settlementRoomModel.PlotsModel, _saveController);
            }

            _battleInfoController = new BattleInfoController(_state);

            _stopController = new BattleRoomStopController(_state, context.Timers, description.WaitingInitTimeoutTime,  _saveController, context.RestartTime, description.RestartRoomTimeoutTime);

            var initAwaiter = new Awaiter<BattleContext>();
            _synchronizationContext = new SynchronizationContext();

            string locationStaticGeometryFileDirectoryPath = LocationStaticGeometryPathDescription.GetServerDirectoryPath(description.ContentDirectoryPath);
            var locationStaticGeometryFile = new LocationStaticGeometryFile(new File(_synchronizationContext), locationStaticGeometryFileDirectoryPath);
            var locationStaticGeometryAsyncLoader = new LocationStaticGeometryAsyncLoader(locationStaticGeometryFile);
            
            _initController = new BattleInitController(initAwaiter, _state, _context, _tick, _saveController, _hangarData, _settlementRoomData, settlementRoomModel, _locationPlotSpotsModel, locationPlotSpotsController, _battleInfoController, description.InitTickScore, locationStaticGeometryAsyncLoader);
            WaitInit(initAwaiter, description);
        }

        private void Init()
        {
        }

        private void Start()
        {
            _settlementRoomModel.PlotsModel.UpdateAfterLoad();
        }

        private async void WaitInit(IAwaiter<BattleContext> initAwaiter, BattleRoomDescription description)
        {
            var battleContext = await initAwaiter;

            var location = battleContext.Location;
            _gauge = _context.Metrics.Get(BattleMetrics.BattleLocations, new BattleLocationsMetricLabels(location));
            _gauge.Inc(1);

            _initializedBattle = new InitializedBattle.InitializedBattle(battleContext, description, _context, _tick, _state, _saveController, _hangarData, _settlementRoomModel, _hangarSyncModel);
        }

        public override IAwaiter<Unit> ReceiveReceipt(Receipt receipt)
        {
            throw new InvalidOperationException();
        }

        public override bool Update(ITick tick)
        {
            if (_state.IsTerminate || (_state.IsStopTsSet && tick.Ts >= _state.StopTs))
            {
                Stop();
                return false;
            }

            _synchronizationContext.Synchronize();
            
            if (_state.IsInitializing)
            {
                _initController.Update();
            }
            else if (_state.IsInitialized)
            {
                _initializedBattle.Update();
            }

            _saveController?.Update();
            _stopController.Update();
            
            _tick.Inc();
            
            return true;
        }

        public override void AddLink(ILink link)
        {
            switch (link.Role)
            {
                case RoleNames.RegionUser:
                    _state.LinkedUsers.Add(link.Room, link);
                    break;
                case RoleNames.BattleMatchmaking:
                    _state.BattleMatchmakingLink = link;
                    break;
                case RoleNames.Party:
                    _state.LinkedParties.Add(link.Room, link);
                    break;
            }

            _initializedBattle?.AddLink(link);
        }

        public override void RemoveLink(string role, string room)
        {
            DownLink(role, room);
        }

        public override void DownLink(string role, string room)
        {
            switch (role)
            {
                case RoleNames.RegionUser:
                    _state.LinkedUsers.Remove(room);
                    break;
                case RoleNames.BattleMatchmaking:
                    _state.BattleMatchmakingLink = null;
                    break;
                case RoleNames.Party:
                    _state.LinkedParties.Remove(room);
                    break;
            }

            _initializedBattle?.DownLink(role, room);
        }

        public override IAwaiter<bool> AddPeer(string peer, IPAddress ipAddress, long ts)
        {
            if (_initializedBattle == null)
            {
                throw new InvalidOperationException();
            }
            
            return _initializedBattle.PeerConnect(peer, ts);
        }

        public override void RemovePeer(string peer, long ts)
        {
            if (_initializedBattle == null)
            {
                throw new InvalidOperationException();
            }
            
            _initializedBattle.PeerDisconnect(peer, ts);
        }

        public override IAwaiter<IEither<IValue, IValue>> ReliablePeerSignal(string peer, IMessage message, long ts) => message switch
        {
            _ when _state.LoadState.IsLoading => _loadingReasonAwaiter,
            _ when _initializedBattle == null => throw new InvalidOperationException(string.Format("message: {0}, peer: {1}", message.Type, peer)),
            _ => _initializedBattle.ReliablePeerSignal(peer, message, ts)
        };

        public override Unit UnreliablePeerSignal(string peer, IMessage message, long ts)
        {
            if (_initializedBattle == null)
            {
                throw new InvalidOperationException(string.Format("message: {0}, peer: {1}", message.Type, peer));
            }
            
            return _initializedBattle.UnreliablePeerSignal(peer, message, ts);
        }

        public override void Terminate()
        {
            _initializedBattle?.Terminate(false);

            RemoveBattleLocationGauge();

            if (_state.IsInitializing)
            {
                _initController.Stop();
            }
        }

        private void Stop()
        {
            _initializedBattle?.Terminate(true);
            
            RemoveBattleLocationGauge();
        }

        private void RemoveBattleLocationGauge()
        {
            if (_gauge != null)
            {
                _gauge.Dec(1);
                _gauge = null;
            }
        }

        public override IMessage BuildMessage(string type) => type switch
        {
            _ when _initializedBattle != null => _initializedBattle.BuildMessage(type),
            _ => new UnknownMessage(type)
        };
    }
}