using System.Collections.Generic;
using Models.Replication.BattleEntityDescription;

namespace Server.Battle.Replication.EntityReplicationDescriptions
{
    public class EntityReplicationDescriptions : IEntityReplicationDescriptions
    {
        private readonly Dictionary<int, BattleEntityReplicationDescription> _descriptions = new();

        public void Add(int id, BattleEntityReplicationDescription battleEntityReplicationDescription)
        {
            _descriptions.Add(id, battleEntityReplicationDescription);
        }

        public void Remove(int id)
        {
            _descriptions.Remove(id);
        }

        public BattleEntityReplicationDescription this[int id] => _descriptions[id];
    }
}