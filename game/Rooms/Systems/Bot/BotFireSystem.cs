using Framework.Ecs.System;
using Framework.Ecs.Tick;
using Models.Models.BattleCharacterStateModel;
using Models.Models.FireModel;
using Models.Prediction.BattleInputModel;

namespace Server.Systems.Bot
{
    public class BotFireSystem : System<(IBattleInputModel battleInputModel, IFireModel fire, IBattleCharacterStateModel stateModel, long createTs)>
    {
        private const long _period = 5000, _halfPeriod = _period / 2;

        protected override void Tick(ITick tick)
        {
            long now = tick.Ts;

            for (int i = 0; i < _count; i++)
            {
                ref var component = ref _components[i];

                var battleInputModel = component.battleInputModel;
                var fire = component.fire;
                var stateModel = component.stateModel;
                var createTs = component.createTs;

                bool isFiring = (now - createTs) % _period > _halfPeriod;
                if (stateModel.Value != BattleCharacterState.Idle) continue;

                if (isFiring)
                {
                    if (!fire.IsInProcess)
                    {
                        battleInputModel.IsFirstShotInQueue = true;
                    }
                    battleInputModel.IsShot = true;
                }
            }
        }
    }
}