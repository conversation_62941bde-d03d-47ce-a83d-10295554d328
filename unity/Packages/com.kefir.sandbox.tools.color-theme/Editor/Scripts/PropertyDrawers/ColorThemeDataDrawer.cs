using System;
using UnityEditor;
using UnityEngine.UIElements;

namespace ColorTheme.Editor
{
    [CustomPropertyDrawer(typeof(ColorData), true)]
    public class ColorThemeDataDrawer : PropertyDrawer
    {
        private const string _templateGuid = "772efada43374eb39be1abe06f402ead";
        
        public override VisualElement CreatePropertyGUI(SerializedProperty property)
        {
            var root = AssetDatabase.LoadAssetAtPath<VisualTreeAsset>(AssetDatabase.GUIDToAssetPath(_templateGuid)).Instantiate();

            var guidProperty = property.FindPropertyRelative(nameof(ColorData.Guid));
            if (string.IsNullOrEmpty(guidProperty.stringValue))
            {
                guidProperty.stringValue = Guid.NewGuid().ToString();
                property.serializedObject.ApplyModifiedProperties();
            }

            return root;
        }
    }
}