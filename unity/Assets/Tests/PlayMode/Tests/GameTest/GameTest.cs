using System.Collections;
using System.IO;
using Game.Shared;
using NUnit.Framework;
using Start;
using System.Diagnostics;
using System.Linq;
using Tests.PlayMode.Adapter;
using Tests.PlayMode.CustomMeasurement;
using Tests.PlayMode.InputWrapper;
using Tests.PlayMode.Logging;
using Tests.PlayMode.ReporterDataCollector;
using Tests.PlayMode.ReporterDataSaver;
using Tests.PlayMode.TestStep.MeasureTimeSteps;
using Tests.PlayMode.Utility;
using Tests.PlayMode.YieldInstruction;
using Unity.Collections;
using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.TestTools;
using Assert = UnityEngine.Assertions.Assert;
using TestStatus = NUnit.Framework.Interfaces.TestStatus;

namespace Tests.PlayMode.Tests.GameTest
{
    public abstract class GameTest<T> : BaseTest where T : IInputWrapper
    {
        private const string _startScenePath = "Assets/Scenes/Start.unity";

        private readonly T _inputWrapper;

        private string _profilerFileName = string.Empty;
        protected IUiAdapter<T> _adapter;
        private UnityEntry _unityEntry;

        protected GameTest(T inputWrapper)
        {
            _inputWrapper = inputWrapper;
        }

        [UnitySetUp]
        public IEnumerator SetUp()
        {
            NativeLeakDetection.Mode = NativeLeakDetectionMode.Disabled;
            
            FilterTest();
            _inputWrapper.Setup();
            Game.Game game;
            ReportDataCollector.Initialize();
            PlayerPrefs.SetInt(PlayerPrefsKeys.EarlyBuildMenuPopup, 1);
            using (MeasureWrapper.ScopeMeasurement(false, null, "StartScreen", "no", "TotalLoadingTime", true))
            {
                using (MeasureWrapper.ScopeMeasurement(false, null, "StartScreen", "no", "SceneLoadingTime", true))
                {
                    var sceneLoading = SceneManager.LoadSceneAsync(_startScenePath);

                    yield return new WaitUntil(() => sceneLoading.isDone);
                    var scene = SceneManager.GetSceneByPath(_startScenePath);
                    Assert.IsTrue(scene.IsValid(), "scene.IsValid()");
                    GameObject gameObject = GameObject.Find("UnityEntry");
                    Assert.IsNotNull(gameObject, "UnityEntry game object != null");

                    _unityEntry = gameObject.GetComponent<UnityEntry>();
                    game = _unityEntry.Game;
                }

                string authenticateFilePath = Path.Combine(Application.persistentDataPath, _unityEntry.Options.SingleSetup.EditorOptions.GameTokenSaveFileName);
                if (File.Exists(authenticateFilePath))
                {
                    File.Delete(authenticateFilePath);
                }

                yield return new StartGameMeasureTimeStep(game.GameModel);
                _inputWrapper.GetVirtualActions();
            }

            _adapter = new UiAdapter<T>(game.GameModel, _inputWrapper, game.RoomConnections);
            yield return new WaitForCondition(() => _adapter.GameModel.GlobalModel.LoadingModel.CurrentLoadScreen is MainMenuLoadScreen, "LoadingModel.CurrentLoadScreen is GlobalMapLoadScreen", 300000);

            SettingsHelper.SelectSettings(_adapter);
            yield return null;
        }

        [UnityTearDown]
        public IEnumerator TearDown()
        {
            if (TestContext.CurrentContext.Result.Outcome.Status != TestStatus.Skipped)
            {
                ReportDataCollector.GetData(TestContext.CurrentContext.Test.Name);
                IReportDataSaver saver = new CsvPerformanceResultDataSaver();
                saver.Send(TestContext.CurrentContext.Test.Name);
                SaveDataToDataBase();
                            
                ReportDataCollector.Reset();
                _adapter = null;
                _inputWrapper.Teardown();
                yield return null;
            }
        }
       
        [Conditional("TEST_SAVE_DATA_TO_SQL")]
        private void SaveDataToDataBase()
        {
            if (TestContext.CurrentContext.Result.Outcome.Status == TestStatus.Passed)
            {
                BigQueryPerformanceResultSaver saverBQ = new BigQueryPerformanceResultSaver();
                saverBQ.Send(TestContext.CurrentContext.Test.Name);
                LogSystem.LogDebug("Data saved to database");
            }
        }
        
        private static void FilterTest()
        {
            if (CommandLineArgsHelper.TryGetArgValue("testFilter", out string testFilter))
            {
                var currentTestFullName = TestContext.CurrentContext.Test.FullName;
                if (testFilter.Split(";").All(filter => !currentTestFullName.Contains(filter)))
                {
                    NUnit.Framework.Assert.Ignore($"Test {currentTestFullName} skipped.");
                }
            }
        }
    }
}