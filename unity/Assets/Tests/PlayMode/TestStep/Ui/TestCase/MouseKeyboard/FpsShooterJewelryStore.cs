using Models.References.Location;
using System.Collections;
using System.Collections.Generic;
using Tests.PlayMode.Adapter;
using Tests.PlayMode.CustomMeasurement;
using Tests.PlayMode.InputWrapper;
using Tests.PlayMode.TestStep.Ui.MouseKeyboard;
using Tests.PlayMode.Utility;
using UnityEngine;

namespace Tests.PlayMode.TestStep.Ui.TestCase.MouseKeyboard
{
    public class FpsShooterJewelryStore : UiStep<IMouseKeyboardInput>
    {
        private float _delay = 3f;

        public FpsShooterJewelryStore(IUiAdapter<IMouseKeyboardInput> adapter) : base(adapter)
        {
        }

        protected override IEnumerator<IEnumerator> AdapterActions(IUiAdapter<IMouseKeyboardInput> adapter)
        {
            yield return new FindRealmStep(adapter);
            yield return new LeaveBattleCheatStep(adapter);
            yield return new FindUniqueShooterStep(adapter, LocationDescription.City);
            yield return new CheatTeleportStep(adapter, PerformanceTeleportRobberyPoints.JewelryStorePoint, PerformanceCameraRotationTeleportPoints.ShooterCameraRotationPoints.JewelryStoreRotation);
            yield return new WaitForSecondsRealtime(_delay);
            yield return MeasureWrapper.FramesMeasurement(true, true, FrameMeasurementHelper.AggregationList, "Shooter", "JewelryStore260D", "FPS");
            yield return new ReturnToStartPositionStep(adapter);
        }
    }
}



