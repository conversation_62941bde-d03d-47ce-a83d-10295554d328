using System;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace LODGroupQuality.Editor
{
    /// <summary>
    /// Data structure for exporting LODQualityRemoveFirstLODOnMobile component information
    /// </summary>
    [Serializable]
    public class LODComponentData
    {
        public string objectFullName;
        public int instanceId;
        public bool recursive;
        public bool forceRemove;
        public bool removeOnLow;

        public LODComponentData(string objectFullName, int instanceId, bool recursive, bool forceRemove, bool removeOnLow)
        {
            this.objectFullName = objectFullName;
            this.instanceId = instanceId;
            this.recursive = recursive;
            this.forceRemove = forceRemove;
            this.removeOnLow = removeOnLow;
        }
    }

    /// <summary>
    /// Container for the exported data
    /// </summary>
    [Serializable]
    public class LODComponentExportData
    {
        public string exportDate;
        public int totalComponentsFound;
        public List<LODComponentData> components;

        public LODComponentExportData()
        {
            exportDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            components = new List<LODComponentData>();
        }
    }

    /// <summary>
    /// Unity Editor tool for exporting LODQualityRemoveFirstLODOnMobile component data
    /// </summary>
    public static class LODQualityComponentExporter
    {
        private const string MENU_PATH = "Kefir/LOD Quality/Export LODQualityRemoveFirstLODOnMobile Components";
        private const string EXPORT_FILENAME = "LODQualityRemoveFirstLODOnMobile_Export.json";

        [MenuItem(MENU_PATH)]
        public static void ExportLODQualityComponents()
        {
            try
            {
                // Create export data container
                var exportData = new LODComponentExportData();
                
                // Get all opened scenes
                int sceneCount = SceneManager.sceneCount;
                if (sceneCount == 0)
                {
                    EditorUtility.DisplayDialog("Export LOD Components", 
                        "No scenes are currently opened. Please open at least one scene before exporting.", 
                        "OK");
                    return;
                }

                // Scan all opened scenes
                for (int i = 0; i < sceneCount; i++)
                {
                    Scene scene = SceneManager.GetSceneAt(i);
                    if (scene.isLoaded)
                    {
                        ScanSceneForLODComponents(scene, exportData.components);
                    }
                }

                // Update total count
                exportData.totalComponentsFound = exportData.components.Count;

                // Show progress dialog
                EditorUtility.DisplayProgressBar("Exporting LOD Components", 
                    $"Found {exportData.totalComponentsFound} components. Saving to JSON...", 0.8f);

                // Convert to JSON
                string json = JsonUtility.ToJson(exportData, true);

                // Save to Assets folder
                string filePath = Path.Combine(Application.dataPath, EXPORT_FILENAME);
                File.WriteAllText(filePath, json);

                // Refresh asset database to show the new file
                AssetDatabase.Refresh();

                // Clear progress bar
                EditorUtility.ClearProgressBar();

                // Show success dialog
                EditorUtility.DisplayDialog("Export Complete", 
                    $"Successfully exported {exportData.totalComponentsFound} LODQualityRemoveFirstLODOnMobile components to:\n{filePath}", 
                    "OK");

                // Ping the created file in the Project window
                string assetPath = "Assets/" + EXPORT_FILENAME;
                var asset = AssetDatabase.LoadAssetAtPath<TextAsset>(assetPath);
                if (asset != null)
                {
                    EditorGUIUtility.PingObject(asset);
                }

                Debug.Log($"LOD Quality Component Export completed. Found {exportData.totalComponentsFound} components in {sceneCount} opened scenes.");
            }
            catch (Exception ex)
            {
                EditorUtility.ClearProgressBar();
                EditorUtility.DisplayDialog("Export Error", 
                    $"An error occurred during export:\n{ex.Message}", 
                    "OK");
                Debug.LogError($"LOD Quality Component Export failed: {ex}");
            }
        }

        /// <summary>
        /// Scans a specific scene for LODQualityRemoveFirstLODOnMobile components
        /// </summary>
        private static void ScanSceneForLODComponents(Scene scene, List<LODComponentData> componentsList)
        {
            // Get all root GameObjects in the scene
            GameObject[] rootObjects = scene.GetRootGameObjects();
            
            foreach (GameObject rootObject in rootObjects)
            {
                // Recursively search for LODQualityRemoveFirstLODOnMobile components
                ScanGameObjectRecursively(rootObject, componentsList);
            }
        }

        /// <summary>
        /// Recursively scans a GameObject and its children for LODQualityRemoveFirstLODOnMobile components
        /// </summary>
        private static void ScanGameObjectRecursively(GameObject gameObject, List<LODComponentData> componentsList)
        {
            // Check if this GameObject has the component
            var lodComponent = gameObject.GetComponent<LODQualityRemoveFirstLODOnMobile>();
            if (lodComponent != null)
            {
                // Get the full hierarchy path
                string fullName = GetGameObjectFullPath(gameObject);
                
                // Create component data
                var componentData = new LODComponentData(
                    fullName,
                    gameObject.GetInstanceID(),
                    lodComponent.recursive,
                    lodComponent.forceRemove,
                    lodComponent.removeOnLow
                );
                
                componentsList.Add(componentData);
            }

            // Recursively check all children
            for (int i = 0; i < gameObject.transform.childCount; i++)
            {
                Transform child = gameObject.transform.GetChild(i);
                ScanGameObjectRecursively(child.gameObject, componentsList);
            }
        }

        /// <summary>
        /// Gets the full hierarchy path of a GameObject
        /// </summary>
        private static string GetGameObjectFullPath(GameObject gameObject)
        {
            string path = gameObject.name;
            Transform parent = gameObject.transform.parent;
            
            while (parent != null)
            {
                path = parent.name + "/" + path;
                parent = parent.parent;
            }
            
            return path;
        }
    }
}
