using System;
using System.Collections.Generic;
using System.Linq;
using Core;
using Framework.Core.Identified;
using UnityEditor;
using UnityEditorInternal;
using UnityEngine;

namespace Utils.ColliderDesigner.Editor.Scene
{
    [CustomEditor(typeof(ViewDescriptions<,>), true)]
    public class ViewDescriptionsEditor : UnityEditor.Editor
    {
        private SerializedProperty _list;
        private Type _type;

        private string[] _ids;
        private readonly List<(string, int)> _warningFields = new();
        private IEnumerable<string> _warningIds;
        private string _searchText = "";
        private readonly List<int> _indexes = new();
        private ReorderableList _reorderableList;
        private bool _isShowItemsList = true;

        private void OnEnable()
        {
            _list = serializedObject.FindProperty("_list");
            _type = target.GetType().BaseType.GetGenericArguments()[0];
            _warningIds = _warningFields.Select(x => x.Item1);

            _reorderableList = new ReorderableList(serializedObject, _list, true, false, true, true)
            {
                drawElementCallback = (rect, index, isActive, isFocused) =>
                {
                    if (_indexes.Count > index)
                    {
                        int realIndex = _indexes[index];
                        SerializedProperty item = _list.GetArrayElementAtIndex(realIndex);
                        rect.height = EditorGUI.GetPropertyHeight(item, true);
                        rect.x += 10;

                        if (TargetPropertyHelper.GetTargetObjectOfProperty(item) is IIdentified identifiedProperty)
                        {
                            EditorGUI.PropertyField(rect, item, new GUIContent(identifiedProperty.Id), true);
                        }
                    }
                },
                elementHeightCallback = index =>
                {
                    if (_indexes.Count > index)
                    {
                        int realIndex = _indexes[index];
                        return EditorGUI.GetPropertyHeight(_list.GetArrayElementAtIndex(realIndex), true);
                    }

                    return 0;
                },
                onReorderCallback = list =>
                {
                    serializedObject.ApplyModifiedProperties();
                }
            };
        }


        public override void OnInspectorGUI()
        {
            serializedObject.Update();

            SerializedProperty property = serializedObject.GetIterator();
            property.NextVisible(true);
            EditorGUILayout.PropertyField(property, true);

            while (property.NextVisible(false))
            {
                if (property.propertyPath != "_list")
                {
                    EditorGUILayout.PropertyField(property, true);
                }
                else
                {
                    _isShowItemsList = EditorGUILayout.Foldout(_isShowItemsList, $"List of {property.arrayElementType}", true);
                    if (_isShowItemsList)
                    {
                        _searchText = EditorGUILayout.TextField("Search (ID):", _searchText);

                        Filter();
                        _reorderableList.draggable = _indexes.Count == _list.arraySize;
                        if (_indexes.Count > 0 || string.IsNullOrEmpty(_searchText))
                        {
                            _reorderableList.DoLayoutList();
                        }
                        else
                        {
                            EditorGUILayout.HelpBox("No items", MessageType.Info);
                        }
                    }
                }
            }


            _warningFields.Clear();
            if (_list.arraySize > 0)
            {
                _ids ??= PropertySetIdentified.GetIDs(_type);

                if (_ids.Length > 0)
                {
                    for (var i = 0; i < _list.arraySize; i++)
                    {
                        var item = _list.GetArrayElementAtIndex(i);

                        if (TargetPropertyHelper.GetTargetObjectOfProperty(item) is IIdentified identifiedProperty && Array.IndexOf(_ids, identifiedProperty.Id) < 0)
                        {
                            _warningFields.Add((identifiedProperty.Id, i));
                        }
                    }
                }
            }

            if (_warningFields.Count > 0)
            {
                EditorGUILayout.HelpBox("List contains unpaired ID" + (_warningFields.Count > 1 ? "s: " : ": ") + string.Join(", ", _warningIds), MessageType.Warning);
                if (GUILayout.Button("Delete unpaired entries"))
                {
                    for (int i = _warningFields.Count - 1; i >= 0; i--)
                    {
                        _list.DeleteArrayElementAtIndex(_warningFields[i].Item2);
                    }

                    _list.serializedObject.ApplyModifiedProperties();
                }
            }

            serializedObject.ApplyModifiedProperties();
        }

        private void Filter()
        {
            _indexes.Clear();
            for (int i = 0; i < _list.arraySize; i++)
            {
                var item = _list.GetArrayElementAtIndex(i);
                if (TargetPropertyHelper.GetTargetObjectOfProperty(item) is IIdentified identified)
                {
                    var id = identified.Id;
                    if (string.IsNullOrEmpty(_searchText) || (id != null && id.ToLower().Contains(_searchText.ToLower())))
                    {
                        _indexes.Add(i);
                    }
                }
            }
        }
    }
}