using System.Linq;
using Utils.ColliderDesigner.Scripts.Triggers;

namespace Utils.ColliderDesigner.Scripts.ExportPlot
{
    public static class FillPlotDataHandler
    {
        public static void Execute(PlotExportModel plotExportModel, PlotTriggerSystemExportModel triggerSystemExportModel)
        {
            var plotView = plotExportModel.PlotView;

            plotView.SpatialAudioParent = plotExportModel.SpatialAudioParent;
            plotView.BuilderPanelView = plotExportModel.BuilderPanel.View;
        }
    }
}