using System;
using System.Linq;
using UnityEditor;
using UnityEngine;
using Object = UnityEngine.Object;

namespace Utils.Editor
{
    public static class GameObjectUtils
    {
        public static void DestroyIfEmpty(GameObject gameObject)
        {
            var components = gameObject.GetComponents(typeof(Component));
            if (components.Length == 1 && gameObject.transform.childCount == 0)
            {
                var parent = gameObject.transform.parent;
                Object.DestroyImmediate(gameObject);
                if (parent)
                {
                    DestroyIfEmpty(parent.gameObject);
                }
            }
        }

        public static void UnpackForEdit(Object @object)
        {
            while (PrefabUtility.GetOutermostPrefabInstanceRoot(@object) is { } parent)
            {
                PrefabUtility.UnpackPrefabInstance(parent, PrefabUnpackMode.OutermostRoot, InteractionMode.AutomatedAction);
            }
        }

        public static void DestroyComponents(this GameObject gameObject, params Type[] typesToDestroy)
        {
            foreach (var type in typesToDestroy)
            {
                while (gameObject.TryGetComponent(type, out var component))
                {
                    Object.DestroyImmediate(component); 
                }
            }
        }

        public static void DestroyRequireComponentDependents(this GameObject gameObject, params Type[] typesToDestroy)
        {
            var components = gameObject.GetComponents<Component>().ToList();

            foreach (var type in typesToDestroy)
            {
                var requiredDependents = components.Where(component =>
                {
                    if (component != null)
                    {
                        var requiredAttribute = (RequireComponent)Attribute.GetCustomAttribute(component.GetType(), typeof(RequireComponent));
                        if (requiredAttribute != null && (requiredAttribute.m_Type0 == type || requiredAttribute.m_Type1 == type || requiredAttribute.m_Type2 == type))
                        {
                            return true;
                        }
                    }
                    return false;
                });

                foreach (var component in requiredDependents)
                {
                    Object.DestroyImmediate(component);
                }
            }
        }

        public static void DestroyWithChildren(GameObject gameObject)
        {
            while (gameObject.transform.childCount > 0)
            {
                DestroyWithChildren(gameObject.transform.GetChild(0).gameObject);
            }
            
            Object.DestroyImmediate(gameObject);
        }
    }
}