using System.Collections.Generic;
using Unity.Collections;
using Unity.Mathematics;
using UnityEngine;
using UnityEngine.Rendering;
using Utils.GlobalWind;
using Utils.Profile;

namespace Utils.Wires
{
    [ExecuteInEditMode]
    public class PoleWire : MonoBehaviour
    {
        private static readonly int _windStrengthID = Shader.PropertyToID("_WindStrength");
        private static readonly int _oscillationAnimationID = Shader.PropertyToID("_OscillationAnimation");
        private static readonly int _randomPhaseOffsetID = Shader.PropertyToID("_RandomPhaseOffset");
        private static readonly int _baseOscillationParallelID = Shader.PropertyToID("_BaseOscillationParallel");
        private static readonly int _baseOscillationPerpendicularID = Shader.PropertyToID("_BaseOscillationPerpendicular");
        private static readonly int _harmonicsAmplitudeParallelID = Shader.PropertyToID("_HarmonicsAmplitudeParallel");
        private static readonly int _harmonicsAmplitudePerpendicularID = Shader.PropertyToID("_HarmonicsAmplitudePerpendicular");
        private static readonly int _tanHalfFovY = Shader.PropertyToID("_WireTanHalfFovY");

        private static MaterialPropertyBlock _mpb;
        
        public WireData[] Wires;

        [SerializeField] private WireAnimationSettings _settings;
        [SerializeField] private Material _material;
        [SerializeField] private int _segments = 8;
        [SerializeField] private int _ringSegments = 4;
        
        private Mesh _mesh;
        private Material _materialInstance;
        private Vector4 _oscillationAnimation;
        private MeshRenderer _meshRenderer;
        private MeshFilter _meshFilter;

        private void OnEnable()
        {
            _oscillationAnimation = Vector4.zero;
            GenerateWireMesh();
            RenderPipelineManager.beginCameraRendering += BeginCameraRender;
        }

        private void OnDisable()
        {
            RenderPipelineManager.beginCameraRendering -= BeginCameraRender;
            CoreUtils.Destroy(_mesh);
            _mesh = null;
            CoreUtils.Destroy(_materialInstance);
            _materialInstance = null;
        }
        
        private void BeginCameraRender(ScriptableRenderContext context, Camera currentCamera)
        {
            if (Application.isPlaying && !currentCamera.CompareTag("MainCamera"))
            {
                return;
            }
            
            Shader.SetGlobalFloat(_tanHalfFovY, Mathf.Tan(0.5f * currentCamera.fieldOfView * Mathf.Deg2Rad));
        }

        private void Update()
        {
            if (_settings ==null)
            {
                return;
            }
            ProfileMarkers.Renderer.Begin();
            if (_meshRenderer == null || _mesh == null || _meshFilter == null || _meshFilter.sharedMesh != _mesh)
            {
                GenerateWireMesh();
            }

            float windSpeed = GlobalWindController.GlobalWindStrength * _settings.GlobalWindStrengthMultiplier;
            float windStrength = _settings.WindStrengthCurve.Evaluate(windSpeed);
            float baseOscillationParallel = _settings.BaseAmplitudeCurveParallel.Evaluate(windSpeed);
            float baseOscillationPerpendicular = _settings.BaseAmplitudeCurvePerpendicular.Evaluate(windSpeed);
            float harmonicsAmplitudeParallel = _settings.HarmonicsAmplitudeCurveParallel.Evaluate(windSpeed);
            float harmonicsAmplitudePerpendicular = _settings.HarmonicsAmplitudeCurvePerpendicular.Evaluate(windSpeed);
            float phaseOffset = _settings.PhaseOffsetCurve.Evaluate(windSpeed);
            
            float oscillationSpeed = _settings.OscillationSpeedCurve.Evaluate(windSpeed);
            _oscillationAnimation.x += Time.deltaTime * oscillationSpeed;

            float harmonicsSpeed = _settings.HarmonicsSpeedCurve.Evaluate(windSpeed);
            _oscillationAnimation.y += Time.deltaTime * 2 * harmonicsSpeed;
            _oscillationAnimation.z += Time.deltaTime * 3 * harmonicsSpeed;
            _oscillationAnimation.w += Time.deltaTime * 5 * harmonicsSpeed;
            
            if (Application.isPlaying)
            {
                _materialInstance.SetFloat(_windStrengthID, windStrength);
                _materialInstance.SetFloat(_baseOscillationParallelID, baseOscillationParallel);
                _materialInstance.SetFloat(_baseOscillationPerpendicularID, baseOscillationPerpendicular);
                _materialInstance.SetFloat(_harmonicsAmplitudeParallelID, harmonicsAmplitudeParallel);
                _materialInstance.SetFloat(_harmonicsAmplitudePerpendicularID, harmonicsAmplitudePerpendicular);
                _materialInstance.SetFloat(_randomPhaseOffsetID, phaseOffset);
                _materialInstance.SetVector(_oscillationAnimationID, _oscillationAnimation);
            }
            else
            {
                _mpb ??= new MaterialPropertyBlock();
                _meshRenderer.GetPropertyBlock(_mpb);
                _mpb.SetFloat(_windStrengthID, windStrength);
                _mpb.SetFloat(_baseOscillationParallelID, baseOscillationParallel);
                _mpb.SetFloat(_baseOscillationPerpendicularID, baseOscillationPerpendicular);
                _mpb.SetFloat(_harmonicsAmplitudeParallelID, harmonicsAmplitudeParallel);
                _mpb.SetFloat(_harmonicsAmplitudePerpendicularID, harmonicsAmplitudePerpendicular);
                _mpb.SetFloat(_randomPhaseOffsetID, phaseOffset);
                _mpb.SetVector(_oscillationAnimationID, _oscillationAnimation);
                _meshRenderer.SetPropertyBlock(_mpb);
            }
            
            ProfileMarkers.Renderer.End();
        }

        public void GenerateWireMesh()
        {
            int totalGaps = 0;
            if (Wires == null || Wires.Length == 0)
            {
                return;
            }

            foreach (var wire in Wires)
            {
                if (wire != null && wire.Poles != null && wire.Poles.Count >= 2)
                    totalGaps += (wire.Poles.Count - 1);
            }

            if (totalGaps == 0)
            {
                Debug.LogError("No valid gaps found among the wires.", gameObject);
                return;
            }

            var verticesPerGap = (_segments + 1) * (_ringSegments + 1);
            var totalVertexCount = totalGaps * verticesPerGap;
            var indicesPerGap = _segments * _ringSegments * 6;
            var totalIndexCount = totalGaps * indicesPerGap;

            var vertexArray = new NativeArray<WireVertex>(totalVertexCount, Allocator.Temp);
            var indexArray = new NativeArray<int>(totalIndexCount, Allocator.Temp);

            var vOffset = 0;
            var iOffset = 0;
            var ringAngleStep = 2 * Mathf.PI / _ringSegments;

            for (int wireIndex = 0; wireIndex < Wires.Length; wireIndex++)
            {
                var wire = Wires[wireIndex];
                if (wire == null || wire.Poles == null || wire.Poles.Count < 2)
                    continue;

                if (wire.SagFactors == null)
                {
                    wire.SagFactors = new List<float>();
                }

                var gapsForWire = wire.Poles.Count - 1;
                while (wire.SagFactors.Count < gapsForWire)
                {
                    wire.SagFactors.Add(0.25f);
                }

                for (int gap = 0; gap < gapsForWire; gap++)
                {
                    var pos0 = wire.Poles[gap].position;
                    var pos1 = wire.Poles[gap + 1].position;
                    var hang = wire.SagFactors[gap];

                    var posAlong = new Vector3[_segments + 1];
                    for (int s = 0; s <= _segments; s++)
                    {
                        float t = (float)s / _segments;
                        posAlong[s] = math.lerp(pos0, pos1, t);
                    }

                    int baseIndex = vOffset;
                    for (int s = 0; s <= _segments; s++)
                    {
                        var t = (float)s / _segments;

                        int prev = (s > 0) ? s - 1 : s;
                        int next = (s < _segments) ? s + 1 : s;
                        var dir = math.normalize(posAlong[next] - posAlong[prev]);

                        var dir0 = math.normalize(math.cross(dir, Vector3.up));
                        if (math.all(dir0 == float3.zero))
                            dir0 = Vector3.right;
                        var dir1 = math.normalize(math.cross(dir0, dir));

                        // ring vertices
                        for (int a = 0; a <= _ringSegments; a++)
                        {
                            var angle = a * ringAngleStep;
                            var ca = Mathf.Cos(angle);
                            var sa = Mathf.Sin(angle);
                            var normal = math.normalize(ca * dir0 + sa * dir1);

                            var vert = new WireVertex
                            {
                                position = posAlong[s],
                                normal = normal,
                                uv0 = new Vector2(s * 2.0f, (float)a / _ringSegments),
                                uv1 = new Vector4(dir0.x, dir0.z, t, hang),
                            };

                            vertexArray[vOffset++] = vert;
                        }
                    }

                    // var baseIndex = gap * verticesPerGap;
                    for (int s = 0; s < _segments; s++)
                    {
                        for (int a = 0; a < _ringSegments; a++)
                        {
                            int i0 = baseIndex + s * (_ringSegments + 1) + a;
                            int i1 = baseIndex + s * (_ringSegments + 1) + a + 1;
                            int i2 = baseIndex + (s + 1) * (_ringSegments + 1) + a;
                            int i3 = baseIndex + (s + 1) * (_ringSegments + 1) + a + 1;

                            indexArray[iOffset++] = i0;
                            indexArray[iOffset++] = i2;
                            indexArray[iOffset++] = i3;

                            indexArray[iOffset++] = i0;
                            indexArray[iOffset++] = i3;
                            indexArray[iOffset++] = i1;
                        }
                    }
                }
            }

            CoreUtils.Destroy(_mesh);
            _mesh = new Mesh
            {
                name = "WireMesh"
            };

            var vertexAttributes = new[]
            {
                new VertexAttributeDescriptor(VertexAttribute.Position, VertexAttributeFormat.Float32, 3),
                new VertexAttributeDescriptor(VertexAttribute.Normal, VertexAttributeFormat.Float32, 3),
                new VertexAttributeDescriptor(VertexAttribute.TexCoord0, VertexAttributeFormat.Float32, 2),
                new VertexAttributeDescriptor(VertexAttribute.TexCoord1, VertexAttributeFormat.Float32, 4),
            };

            _mesh.SetVertexBufferParams(totalVertexCount, vertexAttributes);
            _mesh.SetVertexBufferData(vertexArray, 0, 0, totalVertexCount);
            vertexArray.Dispose();

            _mesh.SetIndexBufferParams(totalIndexCount, IndexFormat.UInt32);
            _mesh.SetIndexBufferData(indexArray, 0, 0, totalIndexCount);
            indexArray.Dispose();

            _mesh.subMeshCount = 1;
            _mesh.SetSubMesh(0, new SubMeshDescriptor(0, totalIndexCount));

            _mesh.RecalculateBounds();
            _mesh.hideFlags = HideFlags.DontSave;
            _mesh.UploadMeshData(true);

            if (!TryGetComponent(out _meshFilter))
            {
                _meshFilter = gameObject.AddComponent<MeshFilter>();
            }

            _meshFilter.sharedMesh = _mesh;
            _meshFilter.hideFlags = HideFlags.DontSave;

            if (!TryGetComponent(out _meshRenderer))
            {
                _meshRenderer = gameObject.AddComponent<MeshRenderer>();
            }

            if (Application.isPlaying)
            {
                _materialInstance = new Material(_material);
                _meshRenderer.sharedMaterial = _materialInstance;
            }
            else
            {
                _meshRenderer.sharedMaterial = _material;
            }
            
            _meshRenderer.hideFlags = HideFlags.DontSave;
        }
    }
}