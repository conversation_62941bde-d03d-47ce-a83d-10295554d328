using System.IO;
using UnityEditor;
using UnityEngine;
using Utils.Editor;

namespace Utils.FBXInjector.Scripts
{
    public class FBXUnpackPostprocessor : AssetPostprocessor
    {
        private string _unpackDirectory;
        
        private void OnPostprocessModel(GameObject g)
        {
            string directory = Path.GetDirectoryName(assetPath);
            string fileName = Path.GetFileNameWithoutExtension(assetPath) + "_Unpack";
            string unpackDataPath = Path.Combine(directory, FileName.WithExtension(fileName, AssetType.Asset));

            var unpackData = AssetDatabase.LoadAssetAtPath<FBXUnpackData>(unpackDataPath);
            if (unpackData && unpackData.MeshFolder && unpackData.DependencyPrefabs.Length > 0)
            {
                new FBXUnpackProcess(unpackData, assetPath);
            }
        }
    }
}