using System.Collections.Generic;
using UnityEngine;
using Utils.Builder.Scripts.Editor.Other;

namespace Utils.Builder.Scripts.Features.Coverage
{
    public class CoverageDataAsset : ScriptableObject, ISelectionBuilderObject
    {
        [SerializeField] internal Vector2[] _vertices;
        [field: SerializeField] public float Rotatation { get; internal set; }
        [field: SerializeField] public float Scale { get; internal set; } = 1;
        [field: SerializeField] public SurfaceTypeValue SurfaceType { get; internal set; } = new();
        [field: SerializeField] public Material Material { get; internal set; }
        [field: SerializeField] public bool CastShadows { get; internal set; }
        [field: SerializeField] public bool NeedRenderer { get; internal set; } = true;

        public IReadOnlyList<Vector2> Vertices => _vertices;

        public static CoverageDataAsset Create(int coveragePoints, float radius, Vector3 position)
        {
            var data = CreateInstance<CoverageDataAsset>();
            data._vertices = FillPoints(new Vector2(position.x, position.z), coveragePoints, radius);
            return data;
        }

        private static Vector2[] FillPoints(Vector2 position, int coveragePoints, float radius)
        {
            var positions = new Vector2[coveragePoints];
            for (var i = 0; i < coveragePoints; i++)
            {
                var rotation = (i + 0.5f) * 360f / coveragePoints * Mathf.Deg2Rad;
                positions[i] = position + new Vector2(Mathf.Cos(rotation) * radius, Mathf.Sin(rotation) * radius);
            }

            return positions;
        }
    }
}