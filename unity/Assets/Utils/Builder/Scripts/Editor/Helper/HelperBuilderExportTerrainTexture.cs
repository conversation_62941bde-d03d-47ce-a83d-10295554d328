using Builder.Helper;
using Core;
using Game.Extensions.Editor;
using UnityEditor;
using UnityEngine;

namespace Builder.Editor.Helper
{
    public static class HelperBuilderExportTerrainTexture
    {
        public static void SwitchNormalMap(Material material, bool normal, params int[] nameIds)
        {
            try
            {
                AssetDatabase.StartAssetEditing();

                foreach (int nameId in nameIds)
                {
                    if (material.GetTexture(nameId) is { } texture)
                    {
                        var importer = (TextureImporter)AssetImporter.GetAtPath(AssetDatabase.GetAssetPath(texture));
                        importer.textureType = normal ? TextureImporterType.NormalMap : TextureImporterType.Default;
                        EditorUtility.SetDirty(importer);
                        importer.SaveAndReimport();
                    }
                }
            }
            finally
            {
                AssetDatabase.StopAssetEditing();
            }
        }

        public static Texture2D BlitMap(ControllerBuilder builder, Material material, Shader shader, int size, bool renderRoads, float addBrightness = 0)
        {
            var renderTexture = new RenderTexture(size, size, 32, RenderTextureFormat.ARGB32, RenderTextureReadWrite.sRGB);
            var outputRect = new Rect(0, 0, size, size);

            var materialBlit = Object.Instantiate(material);
            materialBlit.shader = shader;
            materialBlit.SetFloat(DataBuilderShader.SplatMapWorldSize, builder.Terrain.TerrainScale);
            materialBlit.SetFloat(DataBuilderShader.SplatMapWorldOffset, builder.Terrain.TerrainScale / 2);
            float worldSize = material.GetFloat(DataBuilderShader.SplatMapWorldSize);

            var splitSplatMap = GetSplitTexture(builder.Terrain.TerrainScale, (Texture2D)materialBlit.GetTexture(DataBuilderShader.SplatMap), worldSize);
            var splitNoiseMap = GetSplitTexture(builder.Terrain.TerrainScale, (Texture2D)materialBlit.GetTexture(DataBuilderShader.LayersNoiseMap), worldSize);

            materialBlit.SetTexture(DataBuilderShader.SplatMap, splitSplatMap);
            materialBlit.SetTexture(DataBuilderShader.LayersNoiseMap, splitNoiseMap);
            
            Graphics.Blit(splitSplatMap, renderTexture, materialBlit);

            var mapTexture = new Texture2D(size, size, TextureFormat.RGB24, true);

            RenderTexture.active = renderTexture;
            mapTexture.ReadPixels(outputRect, 0, 0);
            mapTexture.Apply();
            RenderTexture.active = null;

            if (!Mathf.Approximately(addBrightness, 0))
            {
                var pixels = mapTexture.GetPixels();
                for (int i = 0; i < pixels.Length; i++)
                {
                    var pixel = pixels[i];
                    pixels[i] = new Color(pixel.r + addBrightness, pixel.g + addBrightness, pixel.b + addBrightness, pixel.a);
                }

                mapTexture.SetPixels(pixels);
            }

            if (renderRoads)
            {
                var roadsTexture = RenderRoads(builder, size);
                
                var pixels = mapTexture.GetPixels();
                var roadsPixels = roadsTexture.GetPixels();
                for (int i = 0; i < pixels.Length; i++)
                {
                    var pixel = pixels[i];
                    var roadPixel = roadsPixels[i];
                    pixels[i] = Color.Lerp(pixel, new Color(roadPixel.r, roadPixel.g, roadPixel.b), roadPixel.a);
                }

                mapTexture.SetPixels(pixels);
                
                Object.DestroyImmediate(roadsTexture);
            }

            Object.DestroyImmediate(renderTexture);
            Object.DestroyImmediate(materialBlit);
            Object.DestroyImmediate(splitSplatMap);
            Object.DestroyImmediate(splitNoiseMap);
            return mapTexture;
        }

        private static Texture2D GetSplitTexture(float terrainScale, Texture2D texture, float worldSize)
        {
            using var readableTextureScope = new ReadableTextureScope(texture);
            var fromUv = BuilderTerrainPosition.GetUVByPosition(- terrainScale / 2 * Vector3.one, worldSize);
            var toUv = BuilderTerrainPosition.GetUVByPosition(terrainScale / 2 * Vector3.one, worldSize);
            var fromPixelCoords = BuilderTerrainPosition.GetCoordsByUV(fromUv, texture.width);
            var toPixelCoords = BuilderTerrainPosition.GetCoordsByUV(toUv, texture.width);

            int splitWidth = toPixelCoords.x - fromPixelCoords.x;
            int splitHeight = toPixelCoords.y - fromPixelCoords.y;
            var splitPixels = readableTextureScope.Texture2D.GetPixels(fromPixelCoords.x, fromPixelCoords.y, splitWidth, splitHeight);

            var splitSplatMap = new Texture2D(splitWidth, splitHeight, TextureFormat.RGBA32, false, true);
            splitSplatMap.SetPixels(splitPixels);
            splitSplatMap.Apply();
            return splitSplatMap;
        }

        private static Texture2D RenderRoads(ControllerBuilder builder, int size)
        {
            float worldSize = builder.Terrain.TerrainScale;
            float worldOffset = builder.Terrain.TerrainScale / 2;

            float orthographicSize = worldSize / 2;
            float cameraPositionXZ = orthographicSize - worldOffset;
            
            var roadsCamera = new GameObject().AddComponent<Camera>();

            var cameraTransform = roadsCamera.transform;
            cameraTransform.position = new Vector3(cameraPositionXZ, 200, cameraPositionXZ);
            cameraTransform.forward = Vector3.down;

            foreach (var view in builder.RoadViewList.List) view.EnableToCameraRender();
            foreach (var view in builder.CrossroadViewList.List) view.EnableToCameraRender();

            roadsCamera.cullingMask = LayerId.ToMask(LayerId.TerrainBuilderCameraRenderLayer);

            var renderTexture = new RenderTexture(size, size, 24, RenderTextureFormat.ARGB32)
            {
                wrapMode = TextureWrapMode.Clamp,
            };

            HelperBuilderCamera.SetupTerrainTextureBlitCamera(roadsCamera, 0.01f, 400, orthographicSize);


            var light = Object.FindObjectsOfType<Light>(true).FirstOrDefault(l => l.type == LightType.Directional);
            if (light) light.cookie = Texture2D.whiteTexture;
            
            BuilderSetFloorModeToggle.ToggleGizmos(false);

            roadsCamera.targetTexture = renderTexture;
            roadsCamera.Render();

            BuilderSetFloorModeToggle.ToggleGizmos(true);
            
            foreach (var view in builder.RoadViewList.List) view.DisableToCameraRender();
            foreach (var view in builder.CrossroadViewList.List) view.DisableToCameraRender();
            
            var roadsTexture = new Texture2D(size, size, TextureFormat.RGBA32, false);
            RenderTexture.active = renderTexture;
            roadsTexture.ReadPixels(new Rect(0, 0, size, size), 0, 0);
            roadsTexture.Apply();
            RenderTexture.active = null;

            Object.DestroyImmediate(renderTexture);

            Object.DestroyImmediate(roadsCamera.gameObject);

            return roadsTexture;
        }
    }
}