#ifndef WATER_REUSED_SSR_INCLUDED
#define WATER_REUSED_SSR_INCLUDED

#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"

#define SSR_SAMPLES 12
#define SSR_STEPSIZE 0.5
#define SSR_MAX_DISTANCE 100
#define SSR_THICKNESS 1.0

float AttenuateSSR(float2 uv)
{
    float offset = min(1.0 - max(uv.x, uv.y), min(uv.x, uv.y));

    float result = offset / (0.1);
    result = saturate(result);

    return pow(result, 0.5);
}

// Based on StylizedWater2 SSR
void RaymarchSSR(float3 origin, float3 direction, uint samples, half stepSize, half thickness, out half2 sampleUV, out half valid, out half outOfBounds)
{
    sampleUV = 0;
    valid = 0;
    outOfBounds = 0;

    direction *= stepSize;
    const half rcpStepCount = rcp(samples);
 
    UNITY_LOOP
    for(uint i = 0; i <= samples; i++)
    {
        origin += direction;
        direction *= 1+stepSize;

        //View-space to screen-space UV
        sampleUV = ComputeNormalizedDeviceCoordinates(origin, GetViewToHClipMatrix());

        if (any(sampleUV.xy < 0) || any(sampleUV.xy > 1))
        {
            outOfBounds = 1;
            valid = 0;
            break;
        }
        
        outOfBounds = AttenuateSSR(sampleUV);

        //Sample Mip0, gradient sampling cannot work with loops
        float deviceDepth = SAMPLE_TEXTURE2D_X_LOD(_CameraDepthTexture, sampler_ScreenTextures_linear_clamp, sampleUV, 0).r;
        
        //Calculate view-space position from UV and depth
        //Not using the ComputeViewSpacePosition function, since this negates the Z-component
        float3 samplePos = ComputeWorldSpacePosition(sampleUV, deviceDepth, UNITY_MATRIX_I_P);

        if(distance(samplePos.z, origin.z) > length(direction) * thickness) continue;
        
        if(samplePos.z > origin.z)
        {
            valid = 1;
            return;
        }
    }
}

void CalculateSSR(float3 positionWS, float3 reflectionVector, float3 reflectionsFromProbes, out float3 reflectionsColor)
{
        float3 positionVS = TransformWorldToView(positionWS);
        float3 direction = TransformWorldToViewDir(reflectionVector);

        float2 ssrUV = 0;
        half ssrRayMask, ssrEdgeMask = 0;

        RaymarchSSR(positionVS, direction, SSR_SAMPLES, SSR_STEPSIZE, SSR_THICKNESS, ssrUV, ssrRayMask, ssrEdgeMask);

        float3 reflectionSS = SAMPLE_TEXTURE2D_X(_CameraOpaqueTexture, sampler_ScreenTextures_linear_clamp, ssrUV).rgb;
        
        reflectionsColor = lerp(reflectionsFromProbes, reflectionSS, ssrRayMask * ssrEdgeMask);
}
#endif