using Core;
using Core.Updater;

namespace Game.Global.Chat
{
    public class ChatContextViewUpdater : IUpdater
    {
        private readonly ChatContextView _view;
        private readonly ChatContextModel _contextModel;

        public ChatContextViewUpdater(ChatContextView view, ChatContextModel contextModel)
        {
            _view = view;
            _contextModel = contextModel;
        }

        public void Update()
        {
            _view.SetActive(_contextModel.IsOpen);
        }
    }
}