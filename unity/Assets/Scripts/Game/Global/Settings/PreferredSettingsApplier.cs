using System;
using System.Reflection;
using AK.Wwise;
using Game.Common;
using Game.Global.Chat;
using Game.Rendering;
using Inputs;
using LODGroupQuality;
using UnityEngine;
using UnityEngine.Localization.Settings;
using UnityEngine.Rendering.Universal;

namespace Game.Global.Settings
{
    public class PreferredSettingsApplier
    {
        private const string _uiBlurKeyword = "_UI_BLUR_ENABLED";
        
        private readonly GlobalModel _globalModel;
        private readonly UniversalRenderPipelineAsset _asset;
        private readonly FrameRateController _frameRateController;
        private readonly ResolutionViewDescription _resolutionViewDescription;
        private readonly RTPC _masterSound;
        private readonly RTPC _gameMusicVolume;
        private readonly RTPC _radioVolume;
        private readonly RTPC _voicesVolume;
        private readonly RTPC _effectsVolume;
        private readonly LODQualityCategory _characterLODQualityCategory;
        private readonly LODQualityCategory _envLODQualityCategory;
        
        public PreferredSettingsApplier(GlobalModel globalModel, GlobalScreenContent globalScreenContent, UniversalRenderPipelineAsset asset, FrameRateController frameRateController, ResolutionViewDescription resolutionViewDescription)
        {
            _globalModel = globalModel;
            _masterSound = globalScreenContent.MasterSound;
            _gameMusicVolume = globalScreenContent.GameMusicRtpc;
            _radioVolume = globalScreenContent.RadioRtpc;
            _voicesVolume = globalScreenContent.VoicesRtpc;
            _effectsVolume = globalScreenContent.EffectsRtpc;
            _asset = asset;
            _frameRateController = frameRateController;
            _resolutionViewDescription = resolutionViewDescription;
            _characterLODQualityCategory = globalScreenContent.CharactersQualityCategory;
            _envLODQualityCategory = globalScreenContent.EnvironmentQualityCategory;
        }

        public void ApplyGameSettings(GameSettingsModel gameSettingsModel)
        {
            if (gameSettingsModel.Localization.TemporaryValue != "")
            {
                LocalizationSettings.SelectedLocale = LocalizationSettings.AvailableLocales.GetLocale(gameSettingsModel.Localization.TemporaryValue);
            }

            QualitySettings.anisotropicFiltering = gameSettingsModel.AnisotropicFiltering.TemporaryValue;
            QualitySettings.streamingMipmapsMemoryBudget = gameSettingsModel.StreamingMipmapsMemoryBudget.TemporaryValue;
            _characterLODQualityCategory.Quality = (int)gameSettingsModel.CharacterLODQuality.TemporaryValue;
            _envLODQualityCategory.Quality = (int)gameSettingsModel.EnvironmentLODQuality.TemporaryValue;
            _globalModel.InBattleSettingsModel.RotatedReflectionProbesEnabled = gameSettingsModel.RotatedReflectionProbes.TemporaryValue;
            
            QualitySettings.globalTextureMipmapLimit = (int)gameSettingsModel.GlobalTextureMipmapLimit.TemporaryValue;

            _globalModel.InBattleSettingsModel.SetAntiAliasingMode(gameSettingsModel.AntiAliasingMode.CurrentValue, _asset);

            _asset.renderScale = Mathf.Clamp(gameSettingsModel.RenderScale.TemporaryValue, 0.4f, 2f);
            
            ApplyShadowQualityPreset(gameSettingsModel.ShadowsQualityPreset.CurrentValue, gameSettingsModel.AdditionalShadows.TemporaryValue);
            
            _asset.SetAdditionalLightsRenderingMode(gameSettingsModel.AdditionalLights.TemporaryValue == LightRenderingMode.PerVertex ? LightRenderingMode.Disabled : gameSettingsModel.AdditionalLights.TemporaryValue);

            QualitySettings.skinWeights = gameSettingsModel.SkinWeights.TemporaryValue;

            QualitySettings.vSyncCount = gameSettingsModel.VSync.TemporaryValue;

            #if UNITY_IOS || UNITY_ANDROID
             Application.targetFrameRate = gameSettingsModel.FrameRate.TemporaryValue switch
            {
                FrameRateSetting.FPS30 => 30,
                FrameRateSetting.FPS60 => 60,
                FrameRateSetting.Unconstrained => 9999,
                _ => throw new ArgumentOutOfRangeException(nameof(Application.targetFrameRate))
            };
            #elif UNITY_STANDALONE || UNITY_EDITOR
            _frameRateController.TargetFrameRate = gameSettingsModel.FrameRate.TemporaryValue switch
            {
                FrameRateSetting.FPS30 => 30,
                FrameRateSetting.FPS60 => 60,
                FrameRateSetting.Unconstrained => 9999,
                _ => throw new ArgumentOutOfRangeException(nameof(Application.targetFrameRate))
            };
            #endif

            ApplySound(gameSettingsModel);

            _globalModel.SensitivityModel.PlayerPreferenceValue = gameSettingsModel.Sensitivity.TemporaryValue;

            _globalModel.SensitivityModel.PlayerPreferenceAimValue = gameSettingsModel.SensitivityAim.TemporaryValue;

            _globalModel.InBattleSettingsModel.IsPostProcessingEnabled = gameSettingsModel.Postprocessing.TemporaryValue;
            _globalModel.InBattleSettingsModel.IsUseAutoRepeatEnabled = gameSettingsModel.UseAutoRepeat.TemporaryValue;

            _asset.supportsHDR = gameSettingsModel.HDR.TemporaryValue;

            _asset.GetRendererFeature(RenderFeaturesNames.HBAO).SetActive(gameSettingsModel.HBAO.TemporaryValue);
            _globalModel.InBattleSettingsModel.HBAO = gameSettingsModel.HBAO.TemporaryValue;

            _globalModel.InBattleSettingsModel.GrassQualityPreset = gameSettingsModel.GrassQualityPreset.CurrentValue;

            _globalModel.InBattleSettingsModel.GrassVisibleDistanceFactor = gameSettingsModel.GrassVisibleDistanceFactor.TemporaryValue;

            _globalModel.InBattleSettingsModel.WaterQualityPreset = gameSettingsModel.WaterQualityPreset.CurrentValue;

            ShinySSRR.ShinySSRR.isEnabled = gameSettingsModel.ScreenSpaceReflections.TemporaryValue;

            _asset.GetRendererFeature(RenderFeaturesNames.AutoExposure).SetActive(gameSettingsModel.AutoExposure.TemporaryValue);
            _asset.GetRendererFeature(RenderFeaturesNames.MotionBlur).SetActive(gameSettingsModel.MotionBlur.TemporaryValue);
            _asset.GetRendererFeature(RenderFeaturesNames.UIBlur).SetActive(gameSettingsModel.UIBlur.TemporaryValue);
            _globalModel.InBattleSettingsModel.UIBlurEnabled = gameSettingsModel.UIBlur.TemporaryValue;
            if (gameSettingsModel.UIBlur.TemporaryValue)
            {
                Shader.EnableKeyword(_uiBlurKeyword);
            }
            else
            {
                Shader.DisableKeyword(_uiBlurKeyword);
            }

            _globalModel.InBattleSettingsModel.OrdinaryObjectsCullingPreset = gameSettingsModel.OrdinaryObjectsCullingPreset.CurrentValue;
            _globalModel.InBattleSettingsModel.HidingObjectCullingPreset = gameSettingsModel.HidingObjectCullingPreset.CurrentValue;

            _globalModel.InBattleSettingsModel.DepthOfField = gameSettingsModel.DepthOfField.TemporaryValue;

            QualitySettings.lodBias = Mathf.Clamp(gameSettingsModel.LODBias.TemporaryValue, 0f, 1f);
            QualitySettings.enableLODCrossFade = gameSettingsModel.LODCrossFade.TemporaryValue;

            var controls = _globalModel.KeyboardMouseControlsSettingsModel;
            controls.HoldingSprint = gameSettingsModel.SprintHolding.TemporaryValue;

            controls.HoldingCrouch = gameSettingsModel.CrouchHolding.TemporaryValue;

            controls.HoldingAim = gameSettingsModel.AimHolding.TemporaryValue;

            _globalModel.ChatsModel.SystemChatFilterPreference = GetChatPreference(gameSettingsModel);
            ChatNotificationPreference preference = 0;
            preference |= gameSettingsModel.MarkChatsAsUnreadWhenANewMessageArrived.TemporaryValue ? ChatNotificationPreference.Hud : ChatNotificationPreference.None;
            preference |= gameSettingsModel.PlaySoundWhenANewMessageArrived.TemporaryValue ? ChatNotificationPreference.Sound : ChatNotificationPreference.None;
            _globalModel.ChatsModel.ChatNotificationPreference = preference;
            _globalModel.ChatsModel.ShowMiniChat = gameSettingsModel.ShowMiniChat.TemporaryValue;
            _globalModel.ChatsModel.EnableBadWordFilter = gameSettingsModel.EnableBadWordsValidationFilter.TemporaryValue;

#if UNITY_STANDALONE && !UNITY_EDITOR
            var resolution = _resolutionViewDescription.GetResolution(gameSettingsModel.Resolution.TemporaryValue);
            Screen.SetResolution(resolution.x, resolution.y, gameSettingsModel.FullScreen.TemporaryValue);
#endif
        }
        
        private void ApplyShadowQualityPreset(ShadowsQualityPreset shadowQualityPreset, bool castAdditionalShadows)
        {
            _asset.SetMainLightShadows(shadowQualityPreset.CastShadows);
            _asset.SetAdditionalLightsShadows(shadowQualityPreset.CastShadows && castAdditionalShadows);
            
            _asset.SetShadowsResolution((int)shadowQualityPreset.ShadowsResolution);
            
            _asset.cascadeBorder = shadowQualityPreset.ShadowCascadeLastBorder;
            switch (shadowQualityPreset.ShadowCascadeCount)
            {
                case 1:
                    {
                        // Only cascadeBorder defines cascade.
                        break;
                    }
                case 2:
                    {
                        _asset.SetCascadeSplit2(shadowQualityPreset.ShadowCascadeSplit1);
                        break;
                    }
                case 3:
                    {
                        _asset.SetCascadeSplit3(new Vector2(shadowQualityPreset.ShadowCascadeSplit1, shadowQualityPreset.ShadowCascadeSplit2));
                        break;
                    }
                case 4:
                    {
                        _asset.SetCascadeSplit4(new Vector3(shadowQualityPreset.ShadowCascadeSplit1, shadowQualityPreset.ShadowCascadeSplit2, shadowQualityPreset.ShadowCascadeSplit3));
                        break;
                    }
                default:
                    {
                        throw new ArgumentOutOfRangeException(nameof(shadowQualityPreset), $"Unsupported Shadow cascade count: {shadowQualityPreset.ShadowCascadeCount}");
                    }
            }

            _asset.shadowCascadeCount = shadowQualityPreset.ShadowCascadeCount;
            _asset.shadowDistance = shadowQualityPreset.ShadowDistance;
            _globalModel.InBattleSettingsModel.IsShadowsEnabled = shadowQualityPreset.CastShadows;
        }

        public void ApplySound(GameSettingsModel gameSettingsModel)
        {
            _masterSound.SetGlobalValue(gameSettingsModel.MasterVolume.TemporaryValue);
            _gameMusicVolume.SetGlobalValue(gameSettingsModel.GameMusicVolume.TemporaryValue);
            _radioVolume.SetGlobalValue(gameSettingsModel.RadioVolume.TemporaryValue);
            _voicesVolume.SetGlobalValue(gameSettingsModel.VoicesVolume.TemporaryValue);
            _effectsVolume.SetGlobalValue(gameSettingsModel.EffectsVolume.TemporaryValue);
            AkWwiseInitializationSettings.Instance.AdvancedSettings.m_SuspendAudioDuringFocusLoss = !gameSettingsModel.KeepAudioPlayingOnFocusLoss.TemporaryValue;
        }

        private SystemChatPreference GetChatPreference(GameSettingsModel settings)
        {
            if (settings.SystemChatOption.TemporaryValue == SystemChatSetting.All) return SystemChatPreference.All;

            SystemChatPreference preference = SystemChatPreference.None;
            if (settings.SystemChatShowServerMessages.TemporaryValue) preference |= SystemChatPreference.Server;
            if (settings.SystemChatShowClanMessages.TemporaryValue) preference |= SystemChatPreference.Clan;
            if (settings.SystemChatShowPartyMessages.TemporaryValue) preference |= SystemChatPreference.Party;
            if (settings.SystemChatShowBattleMessages.TemporaryValue) preference |= SystemChatPreference.Battle;

            return preference;
        }
    }
}