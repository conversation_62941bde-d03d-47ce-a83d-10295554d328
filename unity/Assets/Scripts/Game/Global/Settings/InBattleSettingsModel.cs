using UnityEngine.Rendering.Universal;
using Utils.Builder.Content.Water.Scripts;
using Utils.Builder.Content.Water.Scripts.Data;
using Utils.Grass.Scripts.Data;
using Utils.RenderersCullingSystem;

namespace Game.Global.Settings
{
    public class InBattleSettingsModel
    {
        public bool IsPostProcessingEnabled { get; set; }
        public bool DepthOfField { get; set; }
        public bool HBAO { get; set; }
        public MsaaQuality MsaaQuality { get; private set; }
        public AntialiasingMode Antialiasing { get; private set; }
        public GrassQualityPreset GrassQualityPreset { get; set; }
        public float GrassVisibleDistanceFactor { get; set; }
        public WaterQualityPreset WaterQualityPreset { get; set; }
        public RenderersCullingPreset OrdinaryObjectsCullingPreset { get; set; }
        public RenderersCullingPreset HidingObjectCullingPreset { get; set; }
        public bool IsShadowsEnabled { get; set; }
        public bool RotatedReflectionProbesEnabled { get; set; }
        public bool UIBlurEnabled { get; set; }
        public bool IsUseAutoRepeatEnabled { get; set; }

        public void SetAntiAliasingMode(AntiAliasingMode antiAliasingMode, UniversalRenderPipelineAsset asset)
        {
            MsaaQuality = antiAliasingMode.MsaaQuality;
            asset.msaaSampleCount = (int)MsaaQuality;
            Antialiasing = antiAliasingMode.AntialiasingMode;
        }
    }
}