using Game.Common;
using Game.Global.ModalDialog;
using Game.Shared;
using UI;
using UnityEngine;

namespace Game.Global.UI.SideMenu.Dialog.Input.Rules
{
    public class RealmLeaveGameDialogRules
    {
        private readonly GlobalScreenContent _globalScreenContent;
        private readonly NetworkStatusModel _networkStatusModel;
        private readonly ModalDialogModel _modalDialogModel;
        private readonly CityBattleStateModel _cityBattleStateModel;

        public RealmLeaveGameDialogRules(GlobalScreenContent globalScreenContent, ModalDialogModel modalDialogModel, NetworkStatusModel networkStatusModel, CityBattleStateModel cityBattleStateModel)
        {
            _globalScreenContent = globalScreenContent;
            _networkStatusModel = networkStatusModel;
            _cityBattleStateModel = cityBattleStateModel;
            _modalDialogModel = modalDialogModel;
        }

        public void OpenLeaveGameDialog()
        {
            var title = new Text(_globalScreenContent.LocalizationKeys.SideMenuKeys.LeaveGameTitle);
            var description = new Text(_globalScreenContent.LocalizationKeys.SideMenuKeys.LeaveGameDescription);
            var toMainMenuText = _globalScreenContent.LocalizationKeys.SideMenuKeys.ToMainMenu;
            var exitGameText = _globalScreenContent.LocalizationKeys.SideMenuKeys.ExitGame;

            _modalDialogModel.CreateBuilder()
                .CreateTwoButtonsDialog(title, description, exitGameText, Validator, QuitGame, toMainMenuText, Validator, ToMainMenu)
                .OpenDialog();
        }

        private void QuitGame()
        {
            _modalDialogModel.CloseDialog();
            Application.Quit();
        }

        private void ToMainMenu()
        {
            if (_cityBattleStateModel.BattleState == null)
            {
                _modalDialogModel.CloseDialog();
                _networkStatusModel.Command = new EnterMainMenuCommand();
            }
        }

        private bool Validator()
        {
            return _networkStatusModel.IsEmpty;
        }
    }
}