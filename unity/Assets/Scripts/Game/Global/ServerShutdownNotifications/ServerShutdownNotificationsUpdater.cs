using Core;
using Core.Updater;
using Framework.Core.Now;
using Game.Global.Chat;
using Game.Shared.Models.Screens;
using Models.Replication;
using System;
using UnityEngine.Localization;
using UnityEngine.Localization.SmartFormat.PersistentVariables;

namespace Game.Global.ServerShutdownNotifications
{
    public class ServerShutdownNotificationsUpdater : IUpdater
    {
        private readonly MaintenanceScreenModel _maintenanceScreenModel;
        private readonly ServerShutdownScreenModel _screenModel;
        private readonly ServerShutdownNotificationsViewDescription _notificationsViewDescription;
        private readonly SystemChatMessageSource _systemChatMessageSource;
        private readonly INow _now;

        private long _currentShutdownTime = 0;
        private long _previousNotificationDelta;

        public ServerShutdownNotificationsUpdater(MaintenanceScreenModel maintenanceScreenModel, ServerShutdownScreenModel screenModel, ServerShutdownNotificationsViewDescription notificationsViewDescription,
            SystemChatMessageSource systemChatMessageSource, INow now)
        {
            _maintenanceScreenModel = maintenanceScreenModel;
            _screenModel = screenModel;
            _notificationsViewDescription = notificationsViewDescription;
            _systemChatMessageSource = systemChatMessageSource;
            _now = now;
        }

        public void Update()
        {
            if (_maintenanceScreenModel.ServerRestartTs is ReplicationUtil.MinTs or ReplicationUtil.MaxTs) return;

            long now = _now.Get();

            if (!_currentShutdownTime.Equals(_maintenanceScreenModel.ServerRestartTs))
            {
                _currentShutdownTime = _maintenanceScreenModel.ServerRestartTs;
                _previousNotificationDelta = _currentShutdownTime - now;
            }

            long timeDiff = _currentShutdownTime - now;

            foreach (var timeItem in _notificationsViewDescription.DeltaTimesForNotification)
            {
                long time = timeItem.Time;

                if (time < timeDiff || time >= _previousNotificationDelta) continue;

                _previousNotificationDelta = time;
                SetNotification(TimeSpan.FromMilliseconds(time), timeItem.TimeState, now);

                return;
            }
        }

        private void SetNotification(TimeSpan timeSpan, ServerShutdownNotificationsViewDescription.TimeState timeState, long now)
        {
            LocalizedString localizedString;

            if (timeSpan.Hours > 0)
            {
                localizedString = _notificationsViewDescription.HoursLocalizedString;
                var hoursRef = localizedString["hours"] as Variable<int>;
                hoursRef.Value = timeSpan.Hours;
            }
            else if (timeSpan.Minutes > 0)
            {
                localizedString = _notificationsViewDescription.MinutesLocalizedString;
                var minutesRef = localizedString["minutes"] as Variable<int>;
                minutesRef.Value = timeSpan.Minutes;
            }
            else
            {
                localizedString = _notificationsViewDescription.SecondsLocalizedString;
                var secondsRef = localizedString["seconds"] as Variable<int>;
                secondsRef.Value = timeSpan.Seconds;
            }

            string message = localizedString.GetLocalizedString();

            if (timeState == ServerShutdownNotificationsViewDescription.TimeState.StaticTime)
            {
                _screenModel.Open(new ServerShutdownTime.Static(timeSpan, _notificationsViewDescription.NotificationDuration));
            }
            else
            {
                _screenModel.Open(new ServerShutdownTime.Dynamic(_currentShutdownTime));
            }

            _systemChatMessageSource
                .NewMessage(message, ChatMessageType.GlobalEvents)
                .WithTime(now)
                .Print();
        }
    }
}