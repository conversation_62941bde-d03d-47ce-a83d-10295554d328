using Core.Updater;

namespace Game.Battle.UiUpdater
{
    public class TooltipMiniClearUpdater : IUpdater
    {
        private readonly TooltipMiniModel _tooltipModel;

        public TooltipMiniClearUpdater(TooltipMiniModel tooltipModel)
        {
            _tooltipModel = tooltipModel;
        }

        public void Update()
        {
            switch (_tooltipModel.Context)
            {
                case TooltipMiniContext.InventoryItem inventoryItemTooltipContext:
                    var isEmpty = inventoryItemTooltipContext.Context.ItemModel.IsEmpty;
                    if (!isEmpty && _tooltipModel.IsOpen)
                    {
                        _tooltipModel.Clear();
                    }

                    break;
            }
        }
    }
}