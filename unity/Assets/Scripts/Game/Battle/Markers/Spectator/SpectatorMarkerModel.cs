using UnityEngine;

namespace Game.Battle.Markers.Spectator
{
    public class SpectatorMarkerModel : IMarkerModel
    {
        public Marker Marker => Marker.Spectator;
        public Vector3 Position { get; set; }
        public SpectatorState State { get; set; }
        public float Progress { get; set; }
        public bool IsProgressFinished { get; set; }

        public SpectatorMarkerModel(Vector3 position, SpectatorState state)
        {
            Position = position;
            State = state;
        }
    }

    public enum SpectatorState
    {
        None = 0,
        Detect = 1,
        Attention = 2,
        Call = 3,
    }
}