using Core;
using UnityEngine;

namespace Game.Locations
{
    public class LightedView : MonoBehaviour
    {
        [SerializeField] private Renderer[] _rendererList;
        
        private bool _instantiated;
        private bool? _value;

        public void SetLight(bool isLight)
        {
            if (_value != isLight)
            {
                _instantiated = true;
                _value = isLight;
                var floatValue = isLight ? 1f : 0f;
                foreach (var renderer in _rendererList)
                {
                    if (renderer == null)
                    {
                        Debug.LogError("No renderer: " + gameObject.name);
                        continue;
                    }
                    foreach (var material in renderer.materials)
                    {
                        if (material == null)
                        {
                            Debug.LogError("No material: " + gameObject.name);
                            continue;
                        }
                        material.SetFloat(ShaderId.IsLight, floatValue);
                    }
                }
            }
        }

        private void OnDestroy()
        {
            if(!_instantiated) return;
            
            foreach (var rend in _rendererList)
            {
                foreach (var material in rend.materials)
                {
                    Destroy(material);
                }
            }
        }
    }
}