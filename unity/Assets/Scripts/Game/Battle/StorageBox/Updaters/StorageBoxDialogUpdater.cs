using Core;
using Core.Updater;
using Game.Battle.EntityModel.StorageBox.Models;
using Game.Battle.Inventory;
using Game.Battle.Inventory.Cost;
using Game.Battle.Inventory.HighlightedItems;
using Game.Battle.LootDialog.Updaters;
using Game.Battle.Storage;
using Game.Battle.Ui;
using Game.Global;
using Game.Global.Updaters;
using Game.Shared.Models;
using Inputs;
using Models.References;
using System;
using System.Collections.Generic;
using UI;
using UnityEngine;

namespace Game.Battle.StorageBox
{
    public class StorageBoxDialogUpdater : IUpdater
    {
        private readonly IUpdater _updater;

        public StorageBoxDialogUpdater(CurrentInventoryModel.StorageBox currentInventoryModel, GlobalModel globalModel, List<IUpdater> mainInputUpdaters, BattleScreenContent battleScreenContent, Transform canvasParent,
            DeviceModel deviceModel,
            BattleModel battleModel, InputActions.BattleActions actions, PersonalNotificationModel notificationModel, Camera uiCamera, IRegisterDisposable registerDisposable)
        {
            var inventoryModels = battleModel.InventoryModels;
            var inventoryDialogModel = battleModel.InventoryDialogModel;
            var gatherHangarCollectionModel = battleModel.PlayerBattleModel.GatherHangarCollectionsModel;
            var battleEntitiesModel = battleModel.BattleEntitiesModel;
            var playerClientModel = battleEntitiesModel.Player.ClientModel;

            var itemMoveContexts = new List<IInventoryItemMoveContext>(currentInventoryModel.InventoryModel.Slots.Count);
            foreach (var slotModel in currentInventoryModel.InventoryModel.Slots.Values)
            {
                itemMoveContexts.Add(new ExternalItemMoveContext(currentInventoryModel.EntityId, currentInventoryModel.InventoryModel, slotModel));
            }

            var storageBoxInventoryEntityModel = (StorageBoxInventoryEntityModel)battleEntitiesModel[currentInventoryModel.EntityId];

            InventoryScreenContent inventoryScreenContent;
            IDisposable disposableContent;
            IUpdater inputUpdater;
            ExternalInventoryDialogView uiView;

            var updaters = new List<IUpdater>();
            _updater = new CompositeUpdater(updaters);
            
            switch (deviceModel.CurrentDeviceId)
            {
                case DeviceId.KeyboardMouse:
                {
                    var viewContent = battleScreenContent.KeyboardExternalInventoryDialogView.Generate(canvasParent);
                    disposableContent = viewContent;
                    uiView = viewContent.Value;
                    inputUpdater = new StorageBoxKeyboardInputHandler(viewContent.Value, storageBoxInventoryEntityModel.EmptySlotsModel, itemMoveContexts, currentInventoryModel, actions, inventoryModels, notificationModel, battleScreenContent.LocalizationKeys.NotificationError, battleModel.TooltipModel, battleModel.TooltipMiniModel, battleEntitiesModel.Player.ClientModel, uiCamera);
                    inventoryScreenContent = battleScreenContent.KeyboardMouseInventoryScreenContent;
                    
                    updaters.Add(new ButtonProgressUpdater(actions.TakeAllItems, viewContent.Value.TakeAllButton));
                    updaters.Add(new InputTextViewUpdater(actions.TakeAllItems, viewContent.Value.TakeAllButton.InputTextView));
                    updaters.Add(new StorageBoxTakeAllDisableInputUpdater(currentInventoryModel.InventoryModel, actions.TakeAllItems, viewContent.Value.TakeAllButton));
                    break;
                }
                case DeviceId.Touch:
                {
                    var viewContent = battleScreenContent.TouchExternalInventoryDialogView.Generate(canvasParent);
                    disposableContent = viewContent;
                    uiView = viewContent.Value;
                    inputUpdater = new StorageBoxTouchInputHandler(viewContent.Value, itemMoveContexts, currentInventoryModel, actions, inventoryModels, notificationModel, battleScreenContent.LocalizationKeys.NotificationError, battleModel.TooltipModel, battleModel.TooltipMiniModel, battleEntitiesModel.Player.ClientModel, uiCamera);
                    inventoryScreenContent = battleScreenContent.TouchInventoryScreenContent;
                    break;
                }
                default:
                    throw new ArgumentOutOfRangeException();
            }
            uiView.ShowTimer(false);
            
            uiView.InventoryCellsUiView.SetLinesCount(storageBoxInventoryEntityModel.ServerModel.InventoryModel.LinesCount);
            var storageBoxModel = battleEntitiesModel.WorldEntitiesModel.StorageByInventory[storageBoxInventoryEntityModel.Id];
            var storageDescription = storageBoxModel.DescriptionModel.Value.Description;
            uiView.SetTitle(battleScreenContent.StorageBoxViewDescriptions[storageDescription].Name);

            if (battleEntitiesModel.WorldEntitiesModel.PlotSlotByEntity.TryGetValue(currentInventoryModel.EntityId, out var plotSlotDescription))
            {
                uiView.ShowOwner(true);
                var hangarPlotModel = battleModel.HangarModel.Slots[plotSlotDescription];
                uiView.SetOwner(hangarPlotModel.OwnerClanTag, hangarPlotModel.OwnerNickname);
            }
            else
            {
                uiView.ShowOwner(false);
            }
            
            mainInputUpdaters.Add(inputUpdater);
            
            updaters.AddRange(new IUpdater[]
            {
                new InventoryDragAndDropModelUpdater(inventoryModels.InventoryDragAndDropModel, uiView.InventoryCellsUiView, itemMoveContexts, currentInventoryModel.InventoryModel, inventoryModels.ClickableItemsModel, uiCamera),
                new UseItemDragAndDropModelUpdater(inventoryModels.InventoryDragAndDropModel, inventoryModels.UseItemDragAndDropViewModel, playerClientModel.UseItemsRule, playerClientModel.BlockedSlotsModel),
                new DragShadowModelUpdater(itemMoveContexts, currentInventoryModel.InventoryModel, storageBoxInventoryEntityModel.ShadowAreaModel, inventoryModels.InventoryDragAndDropModel, uiView.InventoryCellsUiView.CellSize, uiView.InventoryCellsUiView.AreaTransform, playerClientModel.UseItemsRule, uiCamera),
                new InventoryDragShadowValidModelUpdater(itemMoveContexts, currentInventoryModel.InventoryModel, playerClientModel.InventoryIntention, storageBoxInventoryEntityModel.ShadowAreaModel, inventoryModels.InventoryDragAndDropModel),
                new InventoryCellsSlotsUpdater(inventoryModels, gatherHangarCollectionModel, itemMoveContexts, uiView.InventoryCellsUiView, currentInventoryModel, inventoryScreenContent, battleScreenContent, battleModel.SettlementBonusModel, globalModel.BattleRoomModel, battleEntitiesModel.Player, registerDisposable),
                new InventoryDropAreaViewUpdater(uiView.InventoryCellsUiView.DropAreaView, inventoryModels.InventoryDragAndDropModel),
                new DropShadowViewUpdater(uiView.InventoryCellsUiView.DragShadowView, storageBoxInventoryEntityModel.ShadowAreaModel, uiView.InventoryCellsUiView.CellSize, uiView.InventoryCellsUiView.CellPadding, inventoryModels.UseItemDragAndDropViewModel),
                new HighlightedItemsUpdater(inventoryModels.InventoryDragAndDropModel, playerClientModel.UseItemsRule, itemMoveContexts, inventoryModels.HighlightedItemsModel, registerDisposable),
                new TakeAllInteractableUpdater(uiView, storageBoxInventoryEntityModel.EmptySlotsModel),
                new BattleCloseScreenOnHideUpdater(inventoryDialogModel, battleModel.BattleEntitiesModel),
                new ExternalInventoryCloseOnLostBattleEntity(battleEntitiesModel, inventoryDialogModel, currentInventoryModel.EntityId),
                new ResetDialogByDistanceUpdater(battleEntitiesModel.Player.ServerModel, inventoryDialogModel, storageBoxModel.InteractPositionModel, BattleDistancesDescription.DefaultInteractSqrDistance),
                new ResetDialogByRepairedUpdater(storageBoxInventoryEntityModel.Id, battleModel, inventoryDialogModel, storageBoxModel.BreachStateEntityModel),
                new InventoryCostViewUpdater(currentInventoryModel.InventoryModel, uiView.TotalCost, getIsVisible: null),
            });

            
            registerDisposable.Register(() =>
            {
                disposableContent.Dispose();
            });
        }
        
        public void Update()
        {
            _updater.Update();
        }
    }
}