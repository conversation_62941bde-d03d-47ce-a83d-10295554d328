using System.Collections.Generic;
using Models.References.HideObject;

namespace Game.Battle.HideObject
{
    public class HideAndSeekNotificationModel
    {
        private readonly Queue<LocationHideObjectDescription> _startedLocationDescriptions = new ();
        private readonly Queue<LocationHideObjectDescription> _stoppedLocationDescriptions = new ();

        public bool IsEmptyStartedHide => _startedLocationDescriptions.Count == 0;
        public bool IsEmptyStoppedHide => _stoppedLocationDescriptions.Count == 0;
        
        public void EnqueueStartedHidingObject(LocationHideObjectDescription locationHideObjectDescription)
        {
            _startedLocationDescriptions.Enqueue(locationHideObjectDescription);
        }
        
        public void EnqueueStoppedHidingObject(LocationHideObjectDescription locationHideObjectDescription)
        {
            _stoppedLocationDescriptions.Enqueue(locationHideObjectDescription);
        }
        
        public LocationHideObjectDescription DequeueStartedHidingObject()
        {
            return _startedLocationDescriptions.Dequeue();
        }
        
        public LocationHideObjectDescription DequeueStoppedHidingObject()
        {
            return _stoppedLocationDescriptions.Dequeue();
        }
    }
}