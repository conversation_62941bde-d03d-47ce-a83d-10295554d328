using Core;
using Core.Updater;
using Framework.Core.Now;
using Game.Battle.Entities.Cargo.Updaters;
using Game.Battle.EntityModel.CargoExchanger;
using Game.Battle.Map;
using Game.Battle.Target;
using Game.Shared.Models;
using Game.Targets;
using Models.Data;
using Models.MathUtils;
using Models.Models.BattleData;
using Models.References;
using Models.References.Location;
using System.Collections.Generic;
using UnityEngine;

namespace Game.Battle.LocationEvents
{
    public class LocationEventsUpdater : IUpdater
    {
        private readonly Dictionary<LocationEventViewDescription, long> _endLocationEventNotificationsPool = new ();
        private readonly LocationEventModel _locationEventModel;
        private readonly BattleScreenContent _battleScreenContent;
        private readonly GlobalNotificationModel _globalNotificationModel;
        private readonly IBattleData _battleData;
        private readonly BattleModel _battleModel;
        private readonly TargetSubsystem<Collider, UnityRaycastTargetOptions> _raycastTargetSubsystem;
        private readonly IBattleEntitiesModel _battleEntitiesModel;
        private readonly LocationDescription _locationDescription;
        private readonly LocationEventsViewDescription _locationEventsViewDescription;
        private readonly INow _time;
        private PlayerRoleDescription _currentPlayerRole;
        private IRegisterDisposable _takeCargoEventDisposable;
        private IUpdater _updater;
        

        public LocationEventsUpdater(LocationEventModel locationEventModel, BattleScreenContent battleScreenContent, BattleModel battleModel, TargetSubsystem<Collider, UnityRaycastTargetOptions> raycastTargetSubsystem, GlobalNotificationModel globalNotificationModel, IBattleData battleData, IBattleEntitiesModel battleEntitiesModel, LocationDescription locationDescription, LocationEventsViewDescription locationEventsViewDescription, INow time)
        {
            _locationEventModel = locationEventModel;
            _battleScreenContent = battleScreenContent;
            _globalNotificationModel = globalNotificationModel;
            _battleData = battleData;
            _battleEntitiesModel = battleEntitiesModel;
            _locationDescription = locationDescription;
            _battleModel = battleModel;
            _raycastTargetSubsystem = raycastTargetSubsystem;
            _locationEventsViewDescription = locationEventsViewDescription;
            _time = time;
        }
        
        public void Update()
        {
            if (_battleEntitiesModel.Player is { } playerEntityModel)
            {
                if (_currentPlayerRole != playerEntityModel.ServerModel.Role.Value)
                {
                    _currentPlayerRole = playerEntityModel.ServerModel.Role.Value;
                    var currentData = GetLocationEventData(_currentPlayerRole);
                    UpdateModelByPlayerRole(currentData);
                }
                else
                {
                    var currentData = GetLocationEventData(_currentPlayerRole);
                    var description = currentData?.Description;
                    if (description == null)
                    {
                        if (_locationEventModel.HasEvent)
                        {
                            ClearEvent();
                        }
                    }
                    else
                    {
                        if (description.Value != _locationEventModel.Description)
                        {
                            UpdateModelByPlayerRole(currentData);
                        }
                        else
                        {
                            if (NeedUpdateEventPosition(currentData))
                            {
                                UpdateEventPositions(currentData);
                            }

                            if (NeedUpdateEventCustomSettings(currentData))
                            {
                                SetupCustomEventSettings(currentData);
                            }
                        
                            TryCreateNotification();
                        }
                    }
                }
                _updater?.Update();
            }
            if (_endLocationEventNotificationsPool.Count > 0)
            {
                long currentTime = (long)_time.Get;
                foreach (var endEvent in _endLocationEventNotificationsPool)
                {
                    if (currentTime >= endEvent.Value)
                    {
                        _globalNotificationModel.Enqueue(new GlobalNotificationModel.GlobalNotification.LocationEvent(false, endEvent.Key.Title, endEvent.Key.Icon));
                        _endLocationEventNotificationsPool.Remove(endEvent.Key);
                        break;
                    }
                }
            }
        }

        private void UpdateModelByPlayerRole(LocationEventData currentData)
        {
            if (currentData?.Description == null)
            {
                ClearEvent();
            }
            else
            {
                var description = currentData.Description.Value;
                if (description != null)
                {
                    if (description != _locationEventModel.Description)
                    {
                        long endEventTime = currentData.StartTs.Value + description.EventTime;
                        long endPrepareTime = currentData.StartTs.Value + description.PrepareTime;
                        var markerType = GetEventMarker(currentData);
                        _locationEventModel.SetEvent(description, _currentPlayerRole, markerType, currentData.StartTs.Value, endPrepareTime, endEventTime);
                        UpdateEventPositions(currentData);
                        SetupCustomEventSettings(currentData);
                        TryCreateNotification();
                    }
                }
                else
                {
                    ClearEvent();
                }
            }
        }

        private void SetupCustomEventSettings(LocationEventData eventData)
        {
            if (eventData.Description.Value == LocationEventDescription.UnlockContainer)
            {
                foreach (var buildingDescription in _locationDescription.Buildings)
                {
                    if (OBBExtension.IsPointInsideRegion(buildingDescription.Region, _locationEventModel.EventPositions[0].Position))
                    {
                        _locationEventModel.BuildingOwnerDescription = buildingDescription;
                        break;
                    }
                }
            }
            else
            {
                if (eventData.Description.Value == LocationEventDescription.CargoDelivery)
                {
                    _locationEventModel.CargoEntityId = eventData.CargoEntity?.Value ?? BattleEntityIdData.Empty;
                    if (_takeCargoEventDisposable == null && _updater == null)
                    {
                        _takeCargoEventDisposable = new RegisterDisposable();
                        _updater = new CompositeUpdater(new List<IUpdater>()
                        {
                            new LocationEventDeliveryCargoPointsUpdater(_battleModel, _battleEntitiesModel, _battleScreenContent, eventData, _raycastTargetSubsystem, _locationEventsViewDescription, _takeCargoEventDisposable)
                        });
                    }
                }
            }
        }

        private LocationEventData GetLocationEventData(PlayerRoleDescription role)
        {
            if (role == PlayerRoleDescription.Robber)
            {
                return _battleData.RobbersRoleData.LocationEvent;
            }

            return null;
        }

        private void UpdateEventPositions(LocationEventData eventData)
        {
            _locationEventModel.EventPositions.Clear();
            if (eventData.Description.Value == LocationEventDescription.TakeCargo)
            {
                if (eventData.LocationEventCargo?.Value != null)
                {
                    _locationEventModel.EventPositions.Add(new LocationEventModel.LocationEventMarkerPositionModel(eventData.LocationEventCargo.Value.InteractionPoint, true));
                }
            }
            else
            {
                if (eventData.Description.Value == LocationEventDescription.UnlockContainer)
                {
                    if (eventData.LocationEventContainerEntity != null)
                    {
                        int entityId = eventData.LocationEventContainerEntity.Value;
                        if (_locationDescription.TryGetContainer(entityId, out var container))
                        {
                            _locationEventModel.EventPositions.Add(new LocationEventModel.LocationEventMarkerPositionModel(container.Position, true));
                        }
                    }
                }
                else
                {
                    if (eventData.Description.Value == LocationEventDescription.CargoDelivery)
                    {
                        if (eventData.IsCargoAtPosition.Value)
                        {
                            _locationEventModel.EventPositions.Add(new LocationEventModel.LocationEventMarkerPositionModel(eventData.CargoPosition.Value, true));
                        }

                        if (eventData.CargoDeliveryPoints != null)
                        {
                            foreach (var point in eventData.CargoDeliveryPoints)
                            {
                                _locationEventModel.EventPositions.Add(new LocationEventModel.LocationEventMarkerPositionModel(point.Position, false));
                            }
                        }
                    }
                }
            }
        }

        private bool NeedUpdateEventPosition(LocationEventData eventData)
        {
            if (eventData.Description.Value == LocationEventDescription.CargoDelivery)
            {
                return true;
            }
            return false;
        }

        private bool NeedUpdateEventCustomSettings(LocationEventData eventData)
        {
            if (eventData.Description.Value == LocationEventDescription.CargoDelivery)
            {
                if (eventData.CargoEntity == null && _locationEventModel.CargoEntityId != BattleEntityIdData.Empty)
                {
                    return true;
                }

                if (eventData.CargoEntity != null && eventData.CargoEntity.Value != _locationEventModel.CargoEntityId)
                {
                    return true;
                }
            }
            return false;
        }

        private MapMarkerType GetEventMarker(LocationEventData eventData)
        {
            if (eventData.Description.Value == LocationEventDescription.TakeCargo)
            {
                return MapMarkerType.RobberEventTakeCargo;
            }

            if (eventData.Description.Value == LocationEventDescription.CargoDelivery)
            {
                return MapMarkerType.RobberEventCargoDelivery;
            }

            return MapMarkerType.RobberEventUnlockContainer;
        }

        private void TryCreateNotification(bool clearEvent = false)
        {
            if (_locationEventModel.Description != null)
            {
                long currentTime = (long)_time.Get;
                if (currentTime >= _locationEventModel.EndEventTime || clearEvent)
                {
                    var viewDescription = _locationEventsViewDescription[_locationEventModel.Description];
                    if (!_endLocationEventNotificationsPool.ContainsKey(viewDescription))
                    {
                        _endLocationEventNotificationsPool.Add(viewDescription, currentTime + _locationEventsViewDescription.EndEventTimeDuration);
                    }
                }
                else
                {
                    if (currentTime - _locationEventModel.StartEventTime <= TimeDescription.Second && !_locationEventModel.CreateStartNotification)
                    {
                        var viewDescription = _locationEventsViewDescription[_locationEventModel.Description];
                        _globalNotificationModel.Enqueue(new GlobalNotificationModel.GlobalNotification.LocationEvent(true, viewDescription.Title, viewDescription.Icon));
                        _locationEventModel.CreateStartNotification = true;
                    }
                }
            }
        }

        private void ClearEvent()
        {
            _takeCargoEventDisposable?.Dispose();
            _takeCargoEventDisposable = null;
            _updater = null;
            TryCreateNotification(true);
            _locationEventModel.ClearEvent();
        }
    }
}