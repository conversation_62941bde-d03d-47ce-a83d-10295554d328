using System.Collections.Generic;
using Core;
using Core.Updater;
using Game.Battle.TargetHint;
using Game.Locations;
using Game.Targets;
using Models.Models.SettlementCashModel;
using UnityEngine;

namespace Game.Battle.Entities.TaxPoint
{
    public class TaxCashPileTargetHintUpdater : IUpdater
    {
        private readonly TargetModel _targetModel;
        private readonly TargetHintModel _targetHintModel;
        private readonly HashSet<ITargetAction> _actions;
        private readonly Collider[] _collider;
        private readonly TargetHintPositionView _targetPositionHint;
        private readonly ISettlementCashModel _settlementCashModel;
        private bool _isVisible;

        public TaxCashPileTargetHintUpdater(TargetModel targetModel, TargetHintModel targetHintModel, HashSet<ITargetAction> actions, Collider[] collider, TargetHintPositionView targetPositionHint, ISettlementCashModel settlementCashModel, IRegisterDisposable disposable)
        {
            _targetModel = targetModel;
            _targetHintModel = targetHintModel;
            _actions = actions;
            _collider = collider;
            _targetPositionHint = targetPositionHint;
            _settlementCashModel = settlementCashModel;
            
            disposable.Register(() =>
            {
                if (_isVisible)
                {
                    _targetHintModel.Remove(collider);
                }
            });
        }

        public void Update()
        {
            var isVisible = _settlementCashModel.CheckTakeCash();
            if (isVisible != _isVisible)
            {
                _isVisible = isVisible;
                if (isVisible)
                {
                    _targetHintModel.Add(_collider, _targetPositionHint, null, _actions);
                }
                else
                {
                    _targetHintModel.Remove(_collider);
                }
            }
        }
    }
}