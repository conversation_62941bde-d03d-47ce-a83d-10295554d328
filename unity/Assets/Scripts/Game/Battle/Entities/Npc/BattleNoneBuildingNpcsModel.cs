using System.Collections;
using System.Collections.Generic;
using Models.References;
using Models.References.Location;
using Models.References.Location.LocationTraders;
using Models.References.Realm;

namespace Game.Battle.Entities.Npc
{
    public class BattleNoneBuildingNpcsModel : IEnumerable<BattleNpcModel>
    {
        private List<BattleNpcModel> _models = new();
        public BattleNoneBuildingNpcsModel(BattleModeDescription battleMode, LocationDescription locationDescription, SectorDescription sectorDescription)
        {
            foreach (var locationNpcDescription in locationDescription.NpcDescriptions)
            {
                if (!battleMode.NeedSpawnBuildings || locationNpcDescription.TraderInventory != null && locationNpcDescription.VigilantNpcDescription == null)
                {
                    _models.Add(new BattleNpcModel(locationNpcDescription, locationDescription, sectorDescription, battleMode));
                }
            }
        }

        public IEnumerator<BattleNpcModel> GetEnumerator()
        {
            return _models.GetEnumerator();
        }

        IEnumerator IEnumerable.GetEnumerator()
        {
            return GetEnumerator();
        }
    }
}