using Game.Battle.EntityModel.BreachEntity;
using Game.Battle.Player;
using Game.Battle.Target;
using Game.Targets;
using Models.Models.BreachEntityModel;
using Models.Utils.Extensions;
using UnityEngine.Localization;

namespace Game.Battle.Entities.BreachKinematicObjectsController
{
    public class BreachKinematicObjectsControllerTargetProvider : CommonRaycastTargetProvider
    {
        private readonly int _id;
        private readonly IBreachEntityModel _breachEntity;
        private readonly BreachKinematicObjectsControllerTargetActions _actions;
        private readonly BattleModel _battleModel;
        private readonly BattleScreenContent _content;
        private readonly LocalizedString _breachUnavailableByItem;

        public BreachKinematicObjectsControllerTargetProvider(int id, IBreachEntityModel breachEntity, BreachKinematicObjectsControllerTargetActions actions, BattleModel battleModel, BattleScreenContent content)
            : base(new UnityRaycastTargetOptions.ByEntity(id, default), actions.ActionSet)
        {
            _id = id;
            _breachEntity = breachEntity;
            _actions = actions;
            _battleModel = battleModel;
            _content = content;

            LocalizedString referenceString = content.LocalizationKeys.TargetActions.UnavailableKeys.BreachUnavailableBecauseOfItem;
            _breachUnavailableByItem = new LocalizedString(referenceString.TableReference, referenceString.TableEntryReference);
        }

        protected override ITargetAction GetTargetAction()
        {
            if (_battleModel.BattleEntitiesModel.Player is not { } player)
            {
                return null;
            }

            if (OwnerRules.GetOwnerStatus(_id, _battleModel) != OwnerRules.OwnerStatus.EnemyOwner)
            {
                return null;
            }

            var knockoutModel = player.ServerModel.KnockoutModel;
            if (knockoutModel.IsKnockedOut || knockoutModel.IsDelayAfterRevive)
            {
                if (_breachEntity.BreachStateEntityModel.IsActive)
                {
                    return _actions.CloseUnavailableByKnockOut;
                }
                else
                {
                    _actions.BreachKinematic.Invalid(_content.LocalizationKeys.TargetActions.UnavailableKeys.UnavailableWhenKnocked);
                    return _actions.BreachKinematic;
                }
            }

            if (_breachEntity.BreachStateEntityModel.IsActive)
            {
                return _actions.CloseUnavailableByBreach;
            }

            if (!BreachEntityExtensions.FindAnyMatchingBreachItem(_breachEntity.GetBreachItems(), player.ServerModel.PrivateModel.Inventory, out _))
            {
                _breachUnavailableByItem["0"] = BreachEntityViewExtensions.GetBreachItemName(_breachEntity, _content);
                _actions.BreachKinematic.Invalid(_breachUnavailableByItem);
            }
            else
            {
                _actions.BreachKinematic.Valid();
            }

            return _actions.BreachKinematic;
        }
    }
}