using System;
using Content.ContentWrappers;
using Content.PoolCallbackReceiver;
using Core;
using Framework.Core.Identified;
using Models.References.Collection;
using UnityEngine;
using UnityEngine.AddressableAssets;
using UnityEngine.Localization;

namespace Game.Battle.EntityModel.CollectionTable
{
    public class CollectionTableViewDescriptions : ViewDescriptions<CollectionTableDescription, CollectionTableViewDescription>
    {
        [field: SerializeField] public LocalizedString Name { get; private set; }
    }
    
    [Serializable]
    public class CollectionTableViewDescription : IIdentified, ISerializationCallbackReceiver
    {
        [HideInInspector] [SerializeField] private string _id;
        [field: SerializeField] public IdentifiedProperty<CollectionTableDescription> Identified { get; private set; }
        public string Id => Identified.Id;
    
        [SerializeField] private AssetReference _prefab;
    
        public PooledLoadedComponentContent<CollectionTableView> View { get; private set; }
    
        void ISerializationCallbackReceiver.OnBeforeSerialize()
        {
        }
    
        void ISerializationCallbackReceiver.OnAfterDeserialize()
        {
            _id = Identified.Id;
            View = new PooledLoadedComponentContent<CollectionTableView>(_prefab, 0, PoolHideCallbackReceiver.Instance);
        }
    }
}