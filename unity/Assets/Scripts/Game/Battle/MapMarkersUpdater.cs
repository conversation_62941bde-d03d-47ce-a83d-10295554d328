using Content;
using System;
using System.Collections.Generic;
using Content.Disposable;
using Core;
using Core.Updater;
using Framework.Core.Now;
using Game.Battle.LocationEvents;
using Game.Battle.Map;
using Game.Battle.Markers;
using Game.Battle.ZoomLevel;
using Game.Global.Party;
using Game.Locations;
using Inputs;
using UnityEngine;
using Utils.MinimapRenderer;

namespace Game.Battle
{
    public class MapMarkersUpdater : IUpdater
    {
        private const float _eps = 0.001f;
        
        private readonly MapConfigModel _mapConfigModel;
        private readonly MapMeshView _mapMeshView;
        private readonly BattleScreenContent _battleScreenContent;
        private readonly MapMarkersModel _mapMarkersModel;
        private readonly MapMarkerViewDescriptions _mapMarkerViewDescriptions;
        private readonly ColorPartyViewDescription _colorPartyViewDescription;
        private readonly DeviceModel _deviceModel;
        private readonly INow _time;
        private readonly IBattleEntitiesModel _battleEntitiesModel;
        private readonly Dictionary<IMapMarkerModel, (MapMarkerView view, MapMarkerViewDescription viewDescription, IDisposable content)> _markerViews = new();
        private readonly IInstantiateContent<MapMarkerSelectionView> _selectionTemplate;
        private readonly float _sqrRange;
        private readonly float _sqrHeightCheckRange;
        private readonly ZoomLevelActions _zoomLevelActions;
        private readonly MinimapRenderersModel _minimapRenderersModel;
        private readonly List<Renderer> _visibleRenderersForMinimap;

        private float _cameraSize;
        private bool? _isMinimap;
        private IDisposableContent<MapMarkerSelectionView> _activeSelection;
        private IBuildingMapMarkerModel _selectedMarker;
        private bool _isSelectedMarkerVisible;

        public MapMarkersUpdater(BattleModel battleModel, MapMeshView mapMeshView, BattleScreenContent battleScreenContent, DeviceModel deviceModel, ZoomLevelActions zoomLevelActions, IRegisterDisposable registerDisposable)
        {
            _mapConfigModel = battleModel.MapConfigModel;
            _mapMeshView = mapMeshView;
            _battleScreenContent = battleScreenContent;
            _mapMarkersModel = battleModel.MapMarkersModel;
            _mapMarkerViewDescriptions = battleScreenContent.MapMarkerViewDescriptions;
            _colorPartyViewDescription = battleScreenContent.ColorPartyViewDescription;
            _deviceModel = deviceModel;
            _zoomLevelActions = zoomLevelActions;
            _time = battleModel.Time.ServerNow;
            _battleEntitiesModel = battleModel.BattleEntitiesModel;
            _sqrRange = _mapMarkerViewDescriptions.VisibleRange * _mapMarkerViewDescriptions.VisibleRange;
            _sqrHeightCheckRange = _mapMarkerViewDescriptions.ShowHeightDiffRange * _mapMarkerViewDescriptions.ShowHeightDiffRange;
            _minimapRenderersModel = battleModel.MinimapRenderersModel;
            _visibleRenderersForMinimap = new List<Renderer>(256);
            _selectionTemplate = battleScreenContent.MapMarkerSelectionView;
            
            registerDisposable.Register(() =>
            {
                _activeSelection?.Dispose();
                _activeSelection = null;
                
                foreach (var item in _markerViews.Values)
                {
                    item.content.Dispose();
                }

                _markerViews.Clear();
            });
        }

        public void Update()
        {
            RemoveUpdate();
            CreateMarkerUpdate();
            _mapMarkersModel.ClearLists();

            MarkersUpdater();
            _isMinimap = _mapConfigModel.IsMinimap;
            _cameraSize = _mapConfigModel.Config.CameraSize;
        }

        private void RemoveUpdate()
        {
            foreach (var model in _mapMarkersModel.RemoveMarkers)
            {
                if (_markerViews.TryGetValue(model, out var disposableContent))
                {
                    disposableContent.view.StopPulsingAnimation();
                    disposableContent.content.Dispose();
                    _markerViews.Remove(model);
                }
            }
        }

        private void CreateMarkerUpdate()
        {
            foreach (var model in _mapMarkersModel.NewMarkers)
            {
                MapMarkerViewDescription viewDescription = _mapMarkerViewDescriptions[model.MarkerType];
                var viewContent = CreateMarker(model, viewDescription);
                _markerViews.Add(model, (viewContent.view, viewDescription, viewContent.content));
            }
        }

        private (MapMarkerView view, IDisposable content) CreateMarker(IMapMarkerModel model, MapMarkerViewDescription viewDescription)
        {
            MapMarkerView view;
            IDisposable content;
            switch (model)
            {
                case VehicleMapMarkerModel:
                {
                    var viewContent = _battleScreenContent.VehicleMapMarkerView.Generate(_mapMeshView.MarkersContainer.transform);
                    var vehicleMarkerView = viewContent.Value;
                    view = vehicleMarkerView;
                    content = viewContent;
                    break;
                }
                case LocationEventMapMarkerModel:
                {
                    var viewContent = _battleScreenContent.LocationEventMarkerView.Generate(_mapMeshView.MarkersContainer.transform);
                    view = viewContent.Value;
                    content = viewContent;
                    break;
                }
                default:
                {
                    var viewContent = _battleScreenContent.MapMarkerView.Generate(_mapMeshView.MarkersContainer.transform);
                    view = viewContent.Value;
                    content = viewContent;
                    break;
                }
            }

            var color = GetColor(model, viewDescription);
            var mapScale = _deviceModel.CurrentDeviceId == DeviceId.KeyboardMouse ? viewDescription.KeyboardMouseScale : viewDescription.TouchScale;
            var minimapScale = _deviceModel.CurrentDeviceId == DeviceId.KeyboardMouse ? viewDescription.KeyboardMouseMinimapScale : viewDescription.TouchMinimapScale;
            var scale = MapMarkerRules.GetScale(_mapConfigModel, _mapMarkerViewDescriptions, mapScale, minimapScale);

            bool isTurning = IsTurning(model, viewDescription);
            view.SetTurning(isTurning);
            view.SetScale(scale);
            view.SetSprite(viewDescription.Sprite, color);
            Vector3 position = new Vector3(model.Position.x, viewDescription.priority, model.Position.z);
            Quaternion rotation = isTurning ? GetTurningRotation(_mapConfigModel.Config.Rotation) : Quaternion.identity;
            view.SetPositionAndRotation(position, rotation);
            view.SetBorder(viewDescription.Border);
            view.SetEnableMarkerIcon(true);
            view.SetAlpha(1);
            view.HeightIndicatorView.SetHeightDifferenceState(HeightDifference.No);
           
            if (model is AreaMapMarkerModel)
            {
                view.SetSpriteSliced();
            }
            else
            {
                view.SetSpriteSimple();
            }

            if (viewDescription.UsesPulsingAnimation)
            {
                view.StartPulsingAnimation();
            }
            else
            {
                view.StopPulsingAnimation();
            }

            return (view, content);
        }

        // TODO: think about this turning mechanic and billboard shader
        private bool IsTurning(IMapMarkerModel model, MapMarkerViewDescription viewDescription)
        {
            return !_mapConfigModel.IsMinimap || viewDescription.IsTurning || viewDescription.IsShowingHeightDiff || model is VehicleMapMarkerModel;
        }

        private Color GetColor(IMapMarkerModel model, MapMarkerViewDescription viewDescription)
        {
            switch (model)
            {
                case IPartyMapMarkerModel partyMapMarkerModel:
                    if (partyMapMarkerModel.PartyIndex >= 0)
                    {
                        return _colorPartyViewDescription.PartyMemberSlotColors[partyMapMarkerModel.PartyIndex].Color;
                    }
                    break;
                case IClanMapMarkerModel:
                    return _mapMarkerViewDescriptions.ClanColor;
            }
            return viewDescription.Color;
        }

        private void MarkersUpdater()
        {
            _visibleRenderersForMinimap.Clear();

            Quaternion minimapRotation = Quaternion.Euler(0, _mapConfigModel.Config.Rotation, 0);
            Quaternion minimapInversedRotation = Quaternion.Inverse(minimapRotation);
            Vector3 minimapPosition = new Vector3(_mapConfigModel.Config.Position.x, 0, _mapConfigModel.Config.Position.y);
            foreach (var pair in _markerViews)
            {
                var model = pair.Key;
                var view = pair.Value.view;
                var viewDescription = pair.Value.viewDescription;
                var position = new Vector3(model.Position.x, viewDescription.priority, model.Position.z);

                ScaleMarkerUpdate(viewDescription, view);
                UpdateTurning(view, model, viewDescription);
                switch (model)
                {
                    case TimerAlphaMapMarkerModel timerMapMarkerModel:
                    {
                        TimerAlphaUpdate(timerMapMarkerModel, view, viewDescription);
                        break;
                    }
                    case AreaMapMarkerModel areaMapMarkerModel:
                    {
                        view.SetSize(areaMapMarkerModel.Size);
                        var angle = areaMapMarkerModel.RotationModel.Yaw;
                        view.SetPositionAndRotation(position, GetTurningRotation(angle));
                        break;
                    }
                    case VehicleMapMarkerModel vehicleMarkerModel:
                    {
                        var vehicleMarkerView = (VehicleMapMarkerView)view;
                        vehicleMarkerView.SetPosition(position);
                        vehicleMarkerView.SetSeats(vehicleMarkerModel.InVehiclePartyIndexes);

                        if (_isMinimap.HasValue && _isMinimap.Value)
                        {
                            view.SetRotation(_mapConfigModel.Config.Rotation);
                        }

                        foreach (var seatRenderer in vehicleMarkerView.ActiveSeatRenderers)
                        {
                            _visibleRenderersForMinimap.Add(seatRenderer);
                        }

                        break;
                    }
                    case LocationEventMapMarkerModel locationEventMapMarkerModel: 
                    {
                        var locationEventMapMarkerView = (LocationEventMapMarkerView)view;
                        locationEventMapMarkerView.SetPreparingState(locationEventMapMarkerModel.IsPreparing);
                        locationEventMapMarkerView.SetIcon(locationEventMapMarkerModel.MarkerType, locationEventMapMarkerModel.IsCargoAtPosition);
                        if (locationEventMapMarkerModel.IsPreparing)
                        {
                            var deltaTime = (float)((long)_time.Get - locationEventMapMarkerModel.StartTimer) / (locationEventMapMarkerModel.EndTimer - locationEventMapMarkerModel.StartTimer);
                            locationEventMapMarkerView.UpdateTimer(deltaTime);
                        }
                        if (_isMinimap.HasValue && _isMinimap.Value)
                        {
                            view.SetRotation(_mapConfigModel.Config.Rotation, 0);
                        }
                        _visibleRenderersForMinimap.AddRange(locationEventMapMarkerView.Renderers);
                        break;
                    }
                    case IMovableMapMarkerModel movableMapMarkerModel:
                    {
                        if (viewDescription.IsTurning)
                        {
                            float angle = movableMapMarkerModel.RotationModel.Yaw * (movableMapMarkerModel.IsRad ? Mathf.Rad2Deg : 1);
                            view.SetPositionAndRotation(position, GetTurningRotation(angle));
                        }
                        else
                        {
                            view.SetPosition(position);
                        }
                        break;
                    }
                }

                if (viewDescription.IsFar)
                {
                    UpdateFarMarker(view, viewDescription, position, minimapRotation, minimapInversedRotation, minimapPosition, model == _mapConfigModel.MapInfoModel);
                }

                UpdateMarkerVisibility(view, viewDescription, model);
                bool isVisible = view.IsShown && view.IsEnabled && view.GetAlpha() > float.Epsilon;

                if (isVisible)
                {
                    _visibleRenderersForMinimap.Add(view.MarkerRenderer);
                }
                
                if (viewDescription.IsShowingHeightDiff)
                {
                    if (_isMinimap.HasValue && _isMinimap.Value)
                    {
                        view.SetRotation(_mapConfigModel.Config.Rotation);
                    }
                    UpdateHeightMarker(model, view, isVisible);
                }
            }
            
            UpdateMarkerSelection();
            
            _minimapRenderersModel.AddRange(_visibleRenderersForMinimap);
        }

        private void UpdateMarkerSelection()
        {
            IBuildingMapMarkerModel selectedMarker = _mapConfigModel.MapInfoModel; 
            bool hasSelected = selectedMarker != null;
            if (hasSelected && _selectedMarker == null)
            {
                (var view, _, _) = _markerViews[_mapConfigModel.MapInfoModel];
                _activeSelection = _selectionTemplate.Generate();
                UpdateSelectionTransform(view);
                _isSelectedMarkerVisible = view.IsEnabled;
                _activeSelection.Value.SetEnabled(_isSelectedMarkerVisible);
                _visibleRenderersForMinimap.AddRange(_activeSelection.Value.Renderers);
            }
            else if (hasSelected && _markerViews.TryGetValue(_mapConfigModel.MapInfoModel, out var marker))
            {
                var (view, _, _) = marker;
                var isUpdateSelected = selectedMarker != _selectedMarker;
                var isUpdateState = _mapConfigModel.IsMinimap != _isMinimap;
                var isUpdateScale = Math.Abs(_mapConfigModel.Config.CameraSize - _cameraSize) > _eps;
                if (isUpdateScale || isUpdateState || isUpdateSelected)
                {
                    UpdateSelectionTransform(view);
                }
                
                _activeSelection.Value.SetEnabled(_isSelectedMarkerVisible);
                _visibleRenderersForMinimap.AddRange(_activeSelection.Value.Renderers);
            }

            if (!hasSelected && _selectedMarker != null)
            {
                _activeSelection?.Dispose();
            }

            _selectedMarker = selectedMarker;
        }

        private void UpdateSelectionTransform(MapMarkerView markerView)
        {
            var selectedBorderScale = MapMarkerRules.GetScale(_mapConfigModel, _mapMarkerViewDescriptions,
                _deviceModel.CurrentDeviceId == DeviceId.KeyboardMouse ? _mapMarkerViewDescriptions.KeyboardMouseScale : _mapMarkerViewDescriptions.TouchScale,
                _deviceModel.CurrentDeviceId == DeviceId.KeyboardMouse ? _mapMarkerViewDescriptions.KeyboardMouseMinimapScale : _mapMarkerViewDescriptions.TouchMinimapScale);

            _activeSelection.Value.transform.SetParent(markerView.transform, false);
            _activeSelection.Value.SetSelectionLocalScale(selectedBorderScale);
            _activeSelection.Value.UpdateSelectionPosition(_markerViews[_mapConfigModel.MapInfoModel].view.GetSize());
        }

        private void UpdateTurning(MapMarkerView view, IMapMarkerModel model, MapMarkerViewDescription viewDescription)
        {
            if (_isMinimap != _mapConfigModel.IsMinimap)
            {
                bool isTurning = IsTurning(model, viewDescription);
                view.SetTurning(isTurning);
                
                if (isTurning)
                {
                    view.SetRotation(_mapConfigModel.Config.Rotation);
                }
                else
                {
                    view.DiscardRotation();
                }
            }
        }

        private void UpdateHeightMarker(IMapMarkerModel model, MapMarkerView view, bool isVisible)
        {
            if (_battleEntitiesModel.Player == null) return;

            var playerMovementModel = _battleEntitiesModel.Player.ClientModel.SetMovement;

            if (!_mapConfigModel.IsMinimap)
            {
                view.HeightIndicatorView.SetHeightDifferenceState(HeightDifference.No);
                return;
            }

            var pos = model.Position;
            var xzCheckPos = new Vector3(pos.x, 0f, pos.z);
            var xzPlayerPos = new Vector3(playerMovementModel.X, 0, playerMovementModel.Z);
            if ((xzPlayerPos - xzCheckPos).sqrMagnitude >= _sqrHeightCheckRange)
            {
                view.HeightIndicatorView.SetHeightDifferenceState(HeightDifference.No);
                return;
            }

            var heightDiff = playerMovementModel.Y - pos.y;
            if (Mathf.Abs(heightDiff) >= _mapMarkerViewDescriptions.HeightDiffTolerance)
            {
                view.HeightIndicatorView.SetHeightDifferenceState(heightDiff > 0 ? HeightDifference.MarkerIsLower : HeightDifference.MarkerIsHigher);
                if (isVisible)
                {
                    _visibleRenderersForMinimap.Add(view.HeightIndicatorView.Renderer);
                }
            }
            else
            {
                view.HeightIndicatorView.SetHeightDifferenceState(HeightDifference.No);
            }
        }

        private void TimerAlphaUpdate(TimerAlphaMapMarkerModel model, MapMarkerView view, MapMarkerViewDescription viewDescription)
        {
            if (viewDescription.IsFadeAlpha)
            {
                long duration = _mapConfigModel.IsMinimap ? model.Duration : model.MinimapDuration;
                var alpha = GetAlpha(duration, model.StartTs + duration, _time.Get(), _mapMarkerViewDescriptions.fadeDelay, _mapMarkerViewDescriptions.fadeMinAlpha);
                float fadeAlpha = viewDescription.Color.a * alpha;
                view.SetAlpha(fadeAlpha);
            }
        }

        private static float GetAlpha(long duration, long endTs, long time, long fadeDelay, float fadeMinAlpha)
        {
            return time - endTs + duration < fadeDelay
                ? 1
                : 1f * (endTs - time) / (duration - fadeDelay) *
                (1 - fadeMinAlpha) + fadeMinAlpha;
        }

        private void ScaleMarkerUpdate(MapMarkerViewDescription viewDescription, MapMarkerView view)
        {
            var isUpdateState = _mapConfigModel.IsMinimap != _isMinimap;
            var isUpdateScale = Math.Abs(_mapConfigModel.Config.CameraSize - _cameraSize) > _eps;
            if (isUpdateState || isUpdateScale)
            {
                var mapScale = _deviceModel.CurrentDeviceId == DeviceId.KeyboardMouse ? viewDescription.KeyboardMouseScale : viewDescription.TouchScale;
                var minimapScale = _deviceModel.CurrentDeviceId == DeviceId.KeyboardMouse ? viewDescription.KeyboardMouseMinimapScale : viewDescription.TouchMinimapScale;
                var scale = MapMarkerRules.GetScale(_mapConfigModel, _mapMarkerViewDescriptions, mapScale, minimapScale);

                view.SetScale(scale);
            }
        }

        private void UpdateFarMarker(MapMarkerView view, MapMarkerViewDescription markerViewDescription, Vector3 position, Quaternion minimapRotation, Quaternion minimapInversedRotation, Vector3 minimapPosition, bool isThisMarkerSelected)
        {
            if (_isMinimap != _mapConfigModel.IsMinimap)
            {
                if (isThisMarkerSelected)
                {
                    _isSelectedMarkerVisible = true;
                }
                view.SetEnableMarkerIcon(true);
                view.SetPosition(position);
            }

            if (_mapConfigModel.IsMinimap)
            {
                position.y = 0;
                var sqrDistance = new Vector3(_mapConfigModel.Config.Position.x - position.x, 0, _mapConfigModel.Config.Position.y - position.z).sqrMagnitude;
                var (stuckToBorder, borderGlobalPos) = GetPos(position, view.Border, minimapPosition, minimapRotation, minimapInversedRotation);

                var iconPos = borderGlobalPos;
                iconPos.y = markerViewDescription.priority;

                view.SetPosition(iconPos);
                var isVisible = !stuckToBorder || !markerViewDescription.IsFarInRange || sqrDistance <= _sqrRange;
                if (isThisMarkerSelected)
                {
                    _isSelectedMarkerVisible = isVisible;
                }
                
                view.SetEnableMarkerIcon(isVisible);
            }
        }

        private (bool stuckToBorder, Vector3 clampPosition) GetPos(Vector3 markerPosition, BorderDescription border, Vector3 centerPosition, Quaternion minimapRotation, Quaternion minimapInversedRotation)
        {
            var offsetPos = minimapInversedRotation * (markerPosition - centerPosition);

            var excessScaleFactor = GetExcessScaleFactor(offsetPos, border);
            var mapOffsetBorder = excessScaleFactor == 0 ? Vector3.zero
                : excessScaleFactor <= 1f ? offsetPos : offsetPos / excessScaleFactor;

            return (excessScaleFactor >= 1f, minimapRotation * mapOffsetBorder + centerPosition);
        }

        private void UpdateMarkerVisibility(MapMarkerView mapMarkerView, MapMarkerViewDescription mapMarkerViewDescription, IMapMarkerModel mapMarkerModel)
        {
            var needAnim = _mapConfigModel.IsMinimap == _isMinimap;

            if (_mapConfigModel.IsMinimap)
            {
                mapMarkerView.Show(needAnim);
            }
            else
            {
                var mapZoomLevel = _zoomLevelActions.GetMapZoomLevel();
                if (_mapConfigModel.MapInfoModel == mapMarkerModel || mapMarkerViewDescription.VisibilityLevel <= mapZoomLevel)
                {
                    mapMarkerView.Show(needAnim);
                }
                else
                {
                    mapMarkerView.Hide(needAnim);
                }
            }
        }

        private float GetExcessScaleFactor(Vector3 position, BorderDescription border)
        {
            var minimapRect = _mapConfigModel.Config.ResizeContainer.rect;
            var rectWidth = minimapRect.width - (position.x > 0 ? border.RightBorder : border.LeftBorder) * 2;
            var rectHeight = minimapRect.height - (position.z > 0 ? border.UpBorder : border.DownBorder) * 2;
            var cameraSize = rectHeight / minimapRect.height * _mapConfigModel.Config.CameraSize;
            var minimapHalfWidth = rectWidth / rectHeight * cameraSize;
            var excessScaleFactor = Mathf.Max(Mathf.Abs(position.x) / minimapHalfWidth, Mathf.Abs(position.z) / cameraSize);

            return excessScaleFactor;
        }

        private Quaternion GetTurningRotation(float angle) => Quaternion.Euler(90, angle, 0);
    }
}