using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using Object = UnityEngine.Object;

namespace Game.Rendering
{
    public static class URPExtensions
    {
        [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.SubsystemRegistration)]
        private static void ResetCachedFieldInfos()
        {
            _cachedFieldInfo.Clear();
        }
        
        private static readonly Dictionary<string, FieldInfo> _cachedFieldInfo = new();

        public static RTHandle GetRendererTexture(this ScriptableRenderer renderer, string name)
        {
            if (!_cachedFieldInfo.TryGetValue(name, out var fieldInfo))
            {
                fieldInfo = typeof(UniversalRenderer).GetField(name, BindingFlags.NonPublic | BindingFlags.Instance);
                _cachedFieldInfo.Add(name, fieldInfo);
            }
            return fieldInfo.GetValue(renderer) as RTHandle;
        }

        public static Material GetRendererMaterial(this ScriptableRenderer renderer, string name)
        {
            var fieldInfo = typeof(UniversalRenderer).GetField(name, BindingFlags.NonPublic | BindingFlags.Instance);
            return fieldInfo.GetValue(renderer) as Material;
        }

        public static List<ScriptableRendererFeature> GetRendererFeature(this UniversalRenderPipelineAsset asset, string name)
        {
            return asset.GetRendererFeature<ScriptableRendererFeature>(name);
        }

        public static List<T> GetRendererFeature<T>(this UniversalRenderPipelineAsset asset, string name) where T : ScriptableRendererFeature
        {
            var field = asset.GetType().GetField("m_RendererDataList", BindingFlags.Instance | BindingFlags.NonPublic);

            // Multiple renderers can be used each with it's own feature with the searched name.
            var scriptableRenderData = (ScriptableRendererData[])field.GetValue(asset);
            return scriptableRenderData
                .Where(x => x != null)
                .SelectMany(x => x.rendererFeatures)
                .Where(x => x.name == name).Cast<T>().ToList();
        }

        public static void InstantiateAllRenderers(this RenderPipelineAsset asset)
        {
            var type = asset.GetType();
            var field = type.GetField("m_RendererDataList", BindingFlags.Instance | BindingFlags.NonPublic);

            var scriptableRenderData = (ScriptableRendererData[])field.GetValue(asset);

            for (int i = 0; i < scriptableRenderData.Length; i++)
            {
                var duplicateRenderData = Object.Instantiate(scriptableRenderData[i]);
                var originalFeatures = duplicateRenderData.rendererFeatures;
                var duplicateFeatures = new List<ScriptableRendererFeature>(originalFeatures.Count);
                foreach (var rendererFeature in originalFeatures)
                {
                    var duplicateFeature = Object.Instantiate(rendererFeature);
                    duplicateFeature.name = duplicateFeature.name.Replace("(Clone)", "");
                    duplicateFeatures.Add(duplicateFeature);
                }

                SetScriptableRendererDataFeatures(duplicateRenderData, duplicateFeatures);
                scriptableRenderData[i] = duplicateRenderData;
            }

            field.SetValue(asset, scriptableRenderData);
        }

        private static void SetScriptableRendererDataFeatures(ScriptableRendererData scriptableRendererData, List<ScriptableRendererFeature> features)
        {
            var type = scriptableRendererData.GetType();
            var field = type.GetField("m_RendererFeatures", BindingFlags.Instance | BindingFlags.NonPublic);
            field.SetValue(scriptableRendererData, features);
        }

        public static void SetActive(this List<ScriptableRendererFeature> features, bool value)
        {
            foreach (var feature in features)
            {
                feature.SetActive(value);
            }
        }

        public static bool IsActive(this List<ScriptableRendererFeature> features)
        {
            return features.Count > 0 && features.All(x => x.isActive);
        }

        public static void SetAdditionalLightsRenderingMode(this UniversalRenderPipelineAsset asset, LightRenderingMode mode)
        {
            SetPrivateField(asset, "m_AdditionalLightsRenderingMode", mode);
        }

        public static LightRenderingMode GetAdditionalLightsRenderingMode(this UniversalRenderPipelineAsset asset)
        {
            var type = asset.GetType();
            var info = type.GetField("m_AdditionalLightsRenderingMode", BindingFlags.Instance | BindingFlags.NonPublic);
            return (LightRenderingMode)info.GetValue(asset);
        }

        public static void SetAdditionalLightsShadows(this UniversalRenderPipelineAsset asset, bool enable)
        {
            SetPrivateField(asset, "m_AdditionalLightShadowsSupported", enable);
        }

        public static bool GetAdditionalLightsShadows(this UniversalRenderPipelineAsset asset)
        {
            var type = asset.GetType();
            var info = type.GetField("m_AdditionalLightShadowsSupported", BindingFlags.Instance | BindingFlags.NonPublic);
            return (bool)info.GetValue(asset);
        }

        public static void SetMainLightShadows(this UniversalRenderPipelineAsset asset, bool enable)
        {
            SetPrivateField(asset, "m_MainLightShadowsSupported", enable);
        }

        public static void SetShadowsResolution(this UniversalRenderPipelineAsset asset, int resolution)
        {
            SetPrivateField(asset, "m_MainLightShadowmapResolution", resolution);
        }

        public static void SetCascadeSplit2(this UniversalRenderPipelineAsset asset, float cascadeSplit)
        {
            SetPrivateField(asset, "m_Cascade2Split", cascadeSplit);
        }

        public static void SetCascadeSplit3(this UniversalRenderPipelineAsset asset, Vector2 cascadeSplit)
        {
            SetPrivateField(asset, "m_Cascade3Split", cascadeSplit);
        }

        public static void SetCascadeSplit4(this UniversalRenderPipelineAsset asset, Vector3 cascadeSplit)
        {
            SetPrivateField(asset, "m_Cascade4Split", cascadeSplit);
        }

        private static void SetPrivateField<T>(UniversalRenderPipelineAsset asset, string fieldName, T value)
        {
            var type = asset.GetType();
            var fieldInfo = type.GetField(fieldName, BindingFlags.Instance | BindingFlags.NonPublic);
            if (fieldInfo == null)
            {
                Debug.LogErrorFormat("{0} field is null in UniversalRenderPipelineAsset", fieldName);
                return;
            }

            fieldInfo.SetValue(asset, value);
        }
    }
}