using UnityEngine;
using UnityEngine.AddressableAssets;

namespace Content.ContentWrappers
{
    public class NotInstantiateLoadedContent<T> : LoadedContent, INotInstantiateContent<T> where T : Object
    {
        public NotInstantiateLoadedContent(AssetReference reference) : base(reference)
        {
        }

        protected override void AfterLoad()
        {
        }

        protected override void AfterUnload()
        {
        }

        public T Value => (T)_asset;
    }
}