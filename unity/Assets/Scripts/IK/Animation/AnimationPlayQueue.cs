using System.Collections.Generic;
using Cinemachine;
using UnityEngine;
// ReSharper disable CanSimplifyDictionaryLookupWithTryAdd

namespace IK
{
    public class AnimationPlayQueue
    {
        private const int IkSteps = 5;
        private const int BufferSize = 4;
        private const int MaxAnimatedItemsCount = 16;
        private const int TotalBufferSize = BufferSize * MaxAnimatedItemsCount;
        private const float _eps = 0.001f;
        private readonly ITransformSource _transformSource;
        private readonly TransformBuffer[] _transformationBuffers = new TransformBuffer[TotalBufferSize];
        private readonly CinemachineOffsetBuffer[] _cinemachineOffsetBuffers = new CinemachineOffsetBuffer[TotalBufferSize];
        private readonly CameraFovBuffer[] _fovBuffers = new CameraFovBuffer[TotalBufferSize];
        private readonly Dictionary<int, int> _transformBuffersIndexesByHash = new();
        private readonly Dictionary<int, int> _fovBuffersIndexesByHash = new();
        private readonly Dictionary<int, int> _offsetBuffersIndexesByHash = new();
        private readonly Dictionary<int, Transform> _inUse = new();
        private readonly Dictionary<int, CinemachineVirtualCamera> _camerasInUse = new();
        private readonly Dictionary<int, CinemachineCameraOffset> _cameraOffsetsInUse = new();

        private AnimationIKClip _currentClip, _nextClip;
        private float _currentTime, _nextTime;
        private float _blend;
        private int _bufferFrom, _bufferTo, _bufferFallback, _bufferBlendResult;
        private int _inUseTransformCount, _inUseCameraFovCount, _inUseCinemachineOffsetCount;

        public AnimationPlayQueue(ITransformSource transformSource)
        {
            _transformSource = transformSource;
        }

        public void Enqueue(AnimationIKClip clip)
        {
            if (!clip) return;
            
            if (!_currentClip)
            {
                _currentClip = clip;
                _currentTime = 0f;
            }
            else
            {
                _nextClip = clip;
                _nextTime = 0f;
                _blend = 0f;
            }
        }
        
        public void Enqueue(AnimationIKClip clip, float timeOffset)
        {
            if (!clip) return;

            timeOffset = Mathf.Clamp(timeOffset, 0f, clip.Duration);
            if (!_currentClip)
            {
                _currentClip = clip;
                _currentTime = timeOffset;
            }
            else
            {
                _nextClip = clip;
                _nextTime = timeOffset;
                _blend = timeOffset > _nextClip.BlendDuration ? 1f : timeOffset * _nextClip.BlendDurationDiv;
            }
        }

        public void Update(float dt)
        {
            UpdateCurrent(dt);
            UpdateNext(dt);
            UpdateBlend(dt);
        }

        public void Clear()
        {
            _currentClip = _nextClip = null;
            _currentTime = _nextTime = 0f;
            _blend = 0f;
        }

        public void Evaluate(VisibleDistance distance = VisibleDistance.None)
        {
            if (distance == VisibleDistance.Cull) return;

            ClearBuffers();
            UpdateInUse(distance, Buffer.Original);
            ProcessProperties(_currentClip, _currentTime, distance, from: Buffer.Original, to: Buffer.Target1);
            if (_nextClip)
            {
                ProcessProperties(_nextClip, _nextTime, distance, from: Buffer.Original, to: Buffer.Target2);
                BlendTransformationsAndStashInto(source1: Buffer.Target1, source2: Buffer.Target2, blendInto: Buffer.BlendResult, fallback: Buffer.Original);
                ApplyTransformationsFrom(Buffer.BlendResult);
            }
            else
            {
                ApplyTransformationsFrom(Buffer.Target1);
            }
        }

        private void ProcessProperties(AnimationIKClip clip, float time, VisibleDistance distance, int from, int to)
        {
            if (!clip) return;

            float nt = time * clip.DurationDiv;
            _bufferFrom = from;
            _bufferTo = to;
            foreach (AnimationProperty property in clip.Properties)
            {
                if (!ValidateDistance(distance, property)) continue;
                property.Process(this, nt);
            }
        }

        private static bool ValidateDistance(VisibleDistance distance, AnimationProperty property)
        {
            return distance == VisibleDistance.None || distance <= property.Distance || property.Distance == VisibleDistance.None;
        }

        private void UpdateInUse(VisibleDistance distance, int bufferIndex)
        {
            _bufferFrom = bufferIndex;
            if (_currentClip) UpdateInUse(_currentClip, distance);
            if (_nextClip) UpdateInUse(_nextClip, distance);
        }

        private void UpdateInUse(AnimationIKClip clip, VisibleDistance distance)
        {
            foreach (AnimationProperty property in clip.Properties)
            {
                if (!ValidateDistance(distance, property)) continue;
                
                property.UpdateResourcesInUse(this);
            }
        }

        internal void UpdateTransformResourceInUse(int transformHash)
        {
            if (_transformSource.TryGetTransformByHash(transformHash, out Transform transform))
            {
                if (_inUse.TryAdd(transformHash, transform))
                {
                    _transformBuffersIndexesByHash[transformHash] = _inUseTransformCount++;
                }
                
                transform.GetPositionAndRotation(out Vector3 position, out Quaternion orientation);

                _transformationBuffers[_transformBuffersIndexesByHash[transformHash] * BufferSize + _bufferFrom] = new TransformBuffer { Position = position, Orientation = orientation };
            }
        }

        internal void UpdateIKResourcesInUse(int targetHash, int edgeHash, int midHash, int rootHash)
        {
            if (_transformSource.TryGetTransformByHash(targetHash, out Transform target)
                && _transformSource.TryGetTransformByHash(edgeHash, out Transform edge)
                && _transformSource.TryGetTransformByHash(midHash, out Transform mid)
                && _transformSource.TryGetTransformByHash(rootHash, out Transform root))
            {
                if (_inUse.TryAdd(targetHash, target))
                {
                    _transformBuffersIndexesByHash[targetHash] = _inUseTransformCount++;
                }
                if (_inUse.TryAdd(edgeHash, edge))
                {
                    _transformBuffersIndexesByHash[edgeHash] = _inUseTransformCount++;
                }
                if (_inUse.TryAdd(midHash, mid))
                {
                    _transformBuffersIndexesByHash[midHash] = _inUseTransformCount++;
                }
                if (_inUse.TryAdd(rootHash, root))
                {
                    _transformBuffersIndexesByHash[rootHash] = _inUseTransformCount++;
                }

                target.GetPositionAndRotation(out Vector3 position, out Quaternion orientation);
                _transformationBuffers[_transformBuffersIndexesByHash[targetHash] * BufferSize + _bufferFrom] = new TransformBuffer { Position = position, Orientation = orientation };
                
                edge.GetPositionAndRotation(out position, out orientation);
                _transformationBuffers[_transformBuffersIndexesByHash[edgeHash] * BufferSize + _bufferFrom] = new TransformBuffer { Position = position, Orientation = orientation };
                
                mid.GetPositionAndRotation(out position, out orientation);
                _transformationBuffers[_transformBuffersIndexesByHash[midHash] * BufferSize + _bufferFrom] = new TransformBuffer { Position = position, Orientation = orientation };
                
                root.GetPositionAndRotation(out position, out orientation);
                _transformationBuffers[_transformBuffersIndexesByHash[rootHash] * BufferSize + _bufferFrom] = new TransformBuffer { Position = position, Orientation = orientation };
            }
        }

        internal void UpdateCameraResourceInUse(int transformHash)
        {
            if (_transformSource.TryGetTransformByHash(transformHash, out Transform virtCameraTransform) 
                && virtCameraTransform.TryGetComponent(out CinemachineVirtualCamera camera))
            {
                if (_camerasInUse.TryAdd(transformHash, camera))
                {
                    _fovBuffersIndexesByHash[transformHash] = _inUseCameraFovCount++;
                }
                
                _fovBuffers[_fovBuffersIndexesByHash[transformHash] * BufferSize + _bufferFrom] = new CameraFovBuffer { Fov = camera.m_Lens.FieldOfView };
            }
        }
        
        internal void UpdateCameraOffsetResourceInUse(int transformHash)
        {
            if (_transformSource.TryGetTransformByHash(transformHash, out Transform virtCameraTransform) 
                && virtCameraTransform.TryGetComponent(out CinemachineCameraOffset cameraOffset))
            {
                if (_cameraOffsetsInUse.TryAdd(transformHash, cameraOffset))
                {
                    _offsetBuffersIndexesByHash[transformHash] = _inUseCinemachineOffsetCount++;
                }
                
                _cinemachineOffsetBuffers[_offsetBuffersIndexesByHash[transformHash] * BufferSize + _bufferFrom] = new CinemachineOffsetBuffer { Offset = cameraOffset.m_Offset };
            }
        }

        internal void ProcessCameraFov(CameraFovAnimationProperty property, float nt)
        {
            ModifyCamera(property, nt, property.TransformName);
        }
        
        internal void ProcessCameraPosition(CameraPositionAnimationProperty property, float nt)
        {
            ModifyCameraPosition(property, nt, property.TransformName);
        }

        internal void ProcessPosition(PositionAnimationProperty property, float nt)
        {
            ModifyPosition(property, nt, property.TransformName);
        }

        internal void ProcessRotation(RotationAnimationProperty property, float nt)
        {
            ModifyOrientation(property, nt, property.TransformName);
        }

        internal void ProcessIK(IKAnimationProperty property)
        {
            TransformBuffer targetBuffer = _transformationBuffers[_transformBuffersIndexesByHash[property.Target] * BufferSize + _bufferFrom];
            TransformBuffer edgeBuffer = _transformationBuffers[_transformBuffersIndexesByHash[property.Edge] * BufferSize + _bufferFrom];
            TransformBuffer midBuffer = _transformationBuffers[_transformBuffersIndexesByHash[property.Mid] * BufferSize + _bufferFrom];
            TransformBuffer rootBuffer = _transformationBuffers[_transformBuffersIndexesByHash[property.Root] * BufferSize + _bufferFrom];
            
            Vector3 targetPosition = targetBuffer.Position;
            Quaternion targetRotation = targetBuffer.Orientation;

            Quaternion rootRotation = midBuffer.Orientation;
            Quaternion midRotation = midBuffer.Orientation;

            Vector3 edgePosition = edgeBuffer.Position;
            Vector3 midPosition = midBuffer.Position;
            Vector3 rootPosition = rootBuffer.Position;

            Vector3 midToEdge = edgePosition - midPosition;
            Vector3 rootToMid = midPosition - rootPosition;
            float midToEdgeRange = midToEdge.magnitude;
            float rootToMidRange = rootToMid.magnitude;
            
            Vector3 distance = targetPosition - rootPosition;
            float totalMagnitude = midToEdgeRange + rootToMidRange;
            if (distance.sqrMagnitude > totalMagnitude * totalMagnitude)
            {
                Vector3 direction = distance.normalized;
                midPosition = rootPosition + direction * rootToMidRange;
                edgePosition = midPosition + direction * midToEdgeRange;
            }
            else
            {
                for (int i = 0; i < IkSteps; i++)
                {
                    edgePosition = targetPosition;
                    Vector3 direction = (midPosition - edgePosition).normalized;
                    midPosition = edgePosition + direction * midToEdgeRange;
                    direction = (midPosition - rootPosition).normalized;
                    midPosition = rootPosition + direction * rootToMidRange;
                    direction = (edgePosition - midPosition).normalized;
                    edgePosition = midPosition + direction * midToEdgeRange;
                }
            }

            rootRotation = Quaternion.FromToRotation(rootToMid, midPosition - rootPosition) * rootRotation;
            midRotation = Quaternion.FromToRotation(midToEdge, edgePosition - midPosition) * midRotation;

            _transformationBuffers[_transformBuffersIndexesByHash[property.Root] * BufferSize + _bufferTo] = new TransformBuffer
            {
                Position = rootPosition, 
                Orientation = rootRotation, 
                ModifiedSpace = ModifiedSpace.World
            };
            
            _transformationBuffers[_transformBuffersIndexesByHash[property.Mid] * BufferSize + _bufferTo] = new TransformBuffer
            {
                Position = midPosition, 
                Orientation = midRotation, 
                ModifiedSpace = ModifiedSpace.World
            };
            
            _transformationBuffers[_transformBuffersIndexesByHash[property.Edge] * BufferSize + _bufferTo] = new TransformBuffer
            {
                Position = edgePosition, 
                Orientation = targetRotation, 
                ModifiedSpace = ModifiedSpace.World
            };
        }

        internal void ProcessKeepPosition(int transformHash)
        {
            if (_transformSource.TryGetTransformByHash(transformHash, out _))
            {
                KeepOriginalPositionAndRotation(transformHash);
            }
        }

        private void ModifyPosition(PositionAnimationProperty property, float nt, int boneHash)
        {
            if (GetInterval(nt, property.AnimationKeys, out int from, out int to, out float t))
            {
                Transform parent = _inUse[boneHash].parent;
                Vector3 positionOffset = Vector3.Lerp(property.AnimationKeys[from].Position, property.AnimationKeys[to].Position, t);
                Transform target = _transformSource.Target;
                Vector3 offsetPosition = target.TransformDirection(positionOffset);

                int transformIndex = _transformBuffersIndexesByHash[boneHash] * BufferSize;
                int indexFrom = transformIndex + _bufferFrom;
                int indexTo = transformIndex + _bufferTo;
                TransformBuffer bufferFrom = _transformationBuffers[indexFrom];
                TransformBuffer bufferTo = _transformationBuffers[indexTo];
                
                if (bufferTo.ModifiedSpace == ModifiedSpace.World)
                {
                    _transformationBuffers[indexTo] = new TransformBuffer
                    {
                        Position = bufferTo.Position + offsetPosition,
                        Orientation = bufferTo.Orientation,
                        ModifiedSpace = ModifiedSpace.World
                    };
                }
                else if (bufferTo.ModifiedSpace == ModifiedSpace.Local)
                {
                    _transformationBuffers[indexTo] = new TransformBuffer
                    {
                        Position = parent.InverseTransformPoint(bufferFrom.Position + offsetPosition),
                        Orientation = bufferTo.Orientation,
                        ModifiedSpace = ModifiedSpace.Local
                    };
                }
                else
                {
                    _transformationBuffers[indexTo] = new TransformBuffer
                    {
                        Position = bufferFrom.ModifiedSpace == ModifiedSpace.Local ? bufferFrom.Position + offsetPosition : parent.InverseTransformPoint(bufferFrom.Position + offsetPosition),
                        Orientation = Quaternion.Inverse(parent.rotation) * bufferFrom.Orientation,
                        ModifiedSpace = ModifiedSpace.Local
                    };
                }
            }
        }
        
        private void ModifyOrientation(RotationAnimationProperty property, float nt, int boneHash)
        {
            if (GetInterval(nt, property.AnimationKeys, out int from, out int to, out float t))
            {
                Transform parent = _inUse[boneHash].parent;
                
                int transformIndex = _transformBuffersIndexesByHash[boneHash] * BufferSize;
                int indexFrom = transformIndex + _bufferFrom;
                int indexTo = transformIndex + _bufferTo;
                TransformBuffer bufferFrom = _transformationBuffers[indexFrom];
                TransformBuffer bufferTo = _transformationBuffers[indexTo];
                TransformBuffer useBuffer = bufferTo.ModifiedSpace == ModifiedSpace.World ? bufferTo : bufferFrom;
                
                Quaternion rotation = Quaternion.Lerp(property.AnimationKeys[from].Rotation, property.AnimationKeys[to].Rotation, t);
                Quaternion target = _transformSource.Target.rotation;
                Quaternion original = Quaternion.Inverse(target) * useBuffer.Orientation;
                Quaternion modified = rotation * original;
                Quaternion worldOrientation = target * modified;

                if (bufferTo.ModifiedSpace == ModifiedSpace.World)
                {
                    _transformationBuffers[indexTo] = new TransformBuffer
                    {
                        Position = bufferTo.Position,
                        Orientation = worldOrientation,
                        ModifiedSpace = ModifiedSpace.World,
                    };
                }
                else if (bufferTo.ModifiedSpace == ModifiedSpace.Local)
                {
                    _transformationBuffers[indexTo] = new TransformBuffer
                    {
                        Position = bufferTo.Position,
                        Orientation = Quaternion.Inverse(parent.rotation) * worldOrientation,
                        ModifiedSpace = ModifiedSpace.Local,
                    };
                }
                else
                {
                    _transformationBuffers[indexTo] = new TransformBuffer
                    {
                        Position = bufferFrom.ModifiedSpace == ModifiedSpace.Local ? bufferFrom.Position : parent.InverseTransformPoint(bufferFrom.Position),
                        Orientation = Quaternion.Inverse(parent.rotation) * worldOrientation,
                        ModifiedSpace = ModifiedSpace.Local,
                    };
                }
            }
        }

        private void ModifyCamera(CameraFovAnimationProperty property, float nt, int boneHash)
        {
            if (GetInterval(nt, property.AnimationKeys, out int from, out int to, out float t))
            {
                int transformIndex = _transformBuffersIndexesByHash[boneHash] * BufferSize;
                int indexFrom = transformIndex + _bufferFrom;
                int indexTo = transformIndex + _bufferTo;

                CameraFovBuffer fromBuffer = _fovBuffers[indexFrom];
                float newFov = fromBuffer.Fov + Mathf.Lerp(property.AnimationKeys[from].FOV, property.AnimationKeys[to].FOV, t);
                _fovBuffers[indexTo] = new CameraFovBuffer() { Fov = newFov, Modified = Modified.Modified, };
            }
        }        
        
        private void ModifyCameraPosition(CameraPositionAnimationProperty property, float nt, int boneHash)
        {
            if (GetInterval(nt, property.AnimationKeys, out int from, out int to, out float t))
            {
                int transformIndex = _offsetBuffersIndexesByHash[boneHash] * BufferSize;
                int indexFrom = transformIndex + _bufferFrom;
                int indexTo = transformIndex + _bufferTo;

                CinemachineOffsetBuffer bufferFrom = _cinemachineOffsetBuffers[indexFrom];
                Vector3 offset = Vector3.LerpUnclamped(property.AnimationKeys[from].Position, property.AnimationKeys[to].Position, t);
                
                _cinemachineOffsetBuffers[indexTo] = new CinemachineOffsetBuffer()
                {
                    Offset = bufferFrom.Offset + offset,
                    Modified = Modified.Modified,
                };
            }
        }

        private void KeepOriginalPositionAndRotation(int boneHash)
        {
            int transformIndex = _transformBuffersIndexesByHash[boneHash] * BufferSize;
            int indexFrom = transformIndex + _bufferFrom;
            int indexTo = transformIndex + _bufferTo;
            
            TransformBuffer fromBuffer = _transformationBuffers[indexFrom];
            
            _transformationBuffers[indexTo] = new TransformBuffer
            {
                Position = fromBuffer.Position,
                Orientation = fromBuffer.Orientation,
                ModifiedSpace = ModifiedSpace.World,
            };
        }

        private void ClearBuffers()
        {
            for (int i = 0; i < _inUseTransformCount * BufferSize; i++)
            {
                _transformationBuffers[i] = default; 
            }
            
            for (int i = 0; i < _inUseCameraFovCount * BufferSize; i++)
            {
                _fovBuffers[i] = default;
            }
            
            for (int i = 0; i < _inUseCinemachineOffsetCount * BufferSize; i++)
            {
                _cinemachineOffsetBuffers[i] = default;
            }
        }

        private void ApplyTransformationsFrom(int bufferIndex)
        {
            foreach ((int transformHash, Transform transform) in _transformSource.AnimatedTransformsOrder)
            {
                if (_inUse.ContainsKey(transformHash))
                {
                    TransformBuffer buffer = _transformationBuffers[_transformBuffersIndexesByHash[transformHash] * BufferSize + bufferIndex];
                    if (buffer.ModifiedSpace == ModifiedSpace.Local)
                    {
                        transform.SetLocalPositionAndRotation(buffer.Position, buffer.Orientation);
                    }
                    else if (buffer.ModifiedSpace == ModifiedSpace.World)
                    {
                        transform.SetPositionAndRotation(buffer.Position, buffer.Orientation);
                    }
                }

                if (_camerasInUse.TryGetValue(transformHash, out CinemachineVirtualCamera camera))
                {
                    CameraFovBuffer buffer = _fovBuffers[_fovBuffersIndexesByHash[transformHash] * BufferSize + bufferIndex];
                    if (buffer.Modified == Modified.Modified)
                    {
                        camera.m_Lens.FieldOfView = buffer.Fov;
                    }
                }

                if (_cameraOffsetsInUse.TryGetValue(transformHash, out CinemachineCameraOffset cameraOffset))
                {
                    CinemachineOffsetBuffer buffer = _cinemachineOffsetBuffers[_offsetBuffersIndexesByHash[transformHash] * BufferSize + bufferIndex];
                    if (buffer.Modified == Modified.Modified)
                    {
                        cameraOffset.m_Offset = buffer.Offset;
                    }
                }
            }
        }

        private void BlendTransformationsAndStashInto(int source1, int source2, int blendInto, int fallback)
        {
            _bufferFrom = source1;
            _bufferTo = source2;
            _bufferBlendResult = blendInto;
            _bufferFallback = fallback;
            
            foreach ((int transformHash, Transform transform) in _transformSource.AnimatedTransformsOrder)
            {
                if (_inUse.ContainsKey(transformHash))
                {
                    BlendTransform(transformHash, transform);
                }

                if (_camerasInUse.ContainsKey(transformHash))
                {
                    BlendCamera(transformHash);
                }

                if (_cameraOffsetsInUse.ContainsKey(transformHash))
                {
                    BlendCameraOffset(transformHash);
                }
            }
        }

        private void BlendTransform(int transformHash, Transform transform)
        {
            int transformIndex = _transformBuffersIndexesByHash[transformHash] * BufferSize;
            int indexFrom = transformIndex + _bufferFrom;
            int indexTo = transformIndex + _bufferTo;
            int indexFallback = transformIndex + _bufferFallback;
            int indexBlendResult = transformIndex + _bufferBlendResult;

            TransformBuffer from = _transformationBuffers[indexFrom];
            TransformBuffer to = _transformationBuffers[indexTo];

            bool isModified = from.ModifiedSpace != ModifiedSpace.None || to.ModifiedSpace != ModifiedSpace.None;
            if (!isModified) return;

            from = from.ModifiedSpace != ModifiedSpace.None ? from : _transformationBuffers[indexFallback];
            to = to.ModifiedSpace != ModifiedSpace.None ? to : _transformationBuffers[indexFallback];
            
            Transform parent = transform.parent;
            
            (Vector3 localPositionFrom, Quaternion localRotationFrom) = from.ModifiedSpace == ModifiedSpace.Local
                ? (from.Position, from.Orientation)
                : (parent.InverseTransformPoint(from.Position), Quaternion.Inverse(parent.rotation) * from.Orientation);

            (Vector3 localPositionTo, Quaternion localRotationTo) = to.ModifiedSpace == ModifiedSpace.Local
                ? (to.Position, to.Orientation)
                : (parent.InverseTransformPoint(to.Position), Quaternion.Inverse(parent.rotation) * to.Orientation);
            
            Vector3 localPosition = Vector3.Lerp(localPositionFrom, localPositionTo, _blend);
            Quaternion localRotation = Quaternion.Lerp(localRotationFrom, localRotationTo, _blend);
            
            _transformationBuffers[indexBlendResult] = new TransformBuffer
            {
                Position = localPosition,
                Orientation = localRotation,
                ModifiedSpace = ModifiedSpace.Local,
            };
        }

        private void BlendCamera(int transformHash)
        {
            int transformIndex = _transformBuffersIndexesByHash[transformHash] * BufferSize;
            int indexFrom = transformIndex + _bufferFrom;
            int indexTo = transformIndex + _bufferTo;
            int indexFallback = transformIndex + _bufferFallback;
            int indexBlendResult = transformIndex + _bufferBlendResult;
            
            CameraFovBuffer from = _fovBuffers[indexFrom];
            CameraFovBuffer to = _fovBuffers[indexTo];

            bool isModified = from.Modified != Modified.None || to.Modified != Modified.None;
            if (!isModified) return;

            from = from.Modified != Modified.None ? from : _fovBuffers[indexFallback];
            to = to.Modified != Modified.None ? to : _fovBuffers[indexFallback];
            float fov = Mathf.Lerp(from.Fov, to.Fov, _blend);
            _fovBuffers[indexBlendResult] = new CameraFovBuffer { Fov = fov, Modified = Modified.Modified, };
        }
        
        private void BlendCameraOffset(int transformHash)
        {
            int transformIndex = _offsetBuffersIndexesByHash[transformHash] * BufferSize;
            int indexFrom = transformIndex + _bufferFrom;
            int indexTo = transformIndex + _bufferTo;
            int indexFallback = transformIndex + _bufferFallback;
            int indexBlendResult = transformIndex + _bufferBlendResult;

            CinemachineOffsetBuffer from = _cinemachineOffsetBuffers[indexFrom];
            CinemachineOffsetBuffer to = _cinemachineOffsetBuffers[indexTo];

            bool isModified = from.Modified != Modified.None || to.Modified != Modified.None;
            if (!isModified) return;

            from = from.Modified != Modified.None ? from : _cinemachineOffsetBuffers[indexFallback];
            to = to.Modified != Modified.None ? to : _cinemachineOffsetBuffers[indexFallback];

            Vector3 offset = Vector3.Lerp(from.Offset, to.Offset, _blend);
            _cinemachineOffsetBuffers[indexBlendResult] = new CinemachineOffsetBuffer()
            {
                Offset = offset,
                Modified = Modified.Modified,
            };
        }

        private bool GetInterval(float normalizedTime, IReadOnlyList<AnimationPropertyKey> keys, out int from, out int to, out float t)
        {
            for (int i = 0; i < keys.Count - 1; i++)
            {
                AnimationPropertyKey current = keys[i];
                if (current.Time > normalizedTime) continue;
                
                AnimationPropertyKey next = keys[i + 1];
                if (next.Time < normalizedTime) continue;

                from = i;
                to = i + 1;
                t = Mathf.InverseLerp(current.Time, next.Time, normalizedTime);
                t = SmoothTime(t);
                return true;
            }

            from = to = -1;
            t = 0f;
            return false;
        }

        private void UpdateBlend(float dt)
        {
            if (_currentClip && _nextClip)
            {
                if (_nextClip.BlendDuration < float.Epsilon)
                {
                    ReplaceCurrentWithNext();
                    return;
                }

                float blendDurationDiv = _nextClip.BlendDurationDiv;
                _blend = Mathf.MoveTowards(_blend, 1f, dt * blendDurationDiv);

                if (_blend > 1f - _eps)
                {
                    ReplaceCurrentWithNext();
                }
            }
        }

        private void UpdateNext(float dt)
        {
            if (_nextClip)
            {
                _nextTime += dt;
                if (_nextTime > _nextClip.Duration)
                {
                    if (_nextClip.IsLooped)
                    {
                        _nextTime %= _nextClip.Duration;
                    }
                    else
                    {
                        _nextClip = null;
                        _nextTime = 0f;
                    }
                }
            }
        }

        private void UpdateCurrent(float dt)
        {
            if (_currentClip)
            {
                if (_currentTime >= _currentClip.Duration)
                {
                    if (_nextClip)
                    {
                        if (_blend < 1f - _eps) return;
                        ReplaceCurrentWithNext();
                    }
                    else
                    {
                        _currentClip = null;
                    }
                }
                else
                {
                    _currentTime += dt;
                    _currentTime = Mathf.Clamp(_currentTime, 0f, _currentClip.Duration);
                    if (_currentClip.IsLooped)
                    {
                        _currentTime %= _currentClip.Duration;
                    }
                }
            }
        }

        private void ReplaceCurrentWithNext()
        {
            _currentClip = _nextClip;
            _currentTime = _nextTime;
            _nextClip = null;
            _nextTime = 0f;
        }

        private float SmoothTime(float time) => time * time * (3 - 2 * time);

        public bool IsEmpty => _currentClip == null;
    }
}