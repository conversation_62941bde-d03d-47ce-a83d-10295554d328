using System;
using TMPro;
using UnityEditor;
using UnityEditor.Events;
using UnityEngine.Events;
using UnityEngine.Localization.Components;

namespace UI.Editor
{
    public static class LocalizationUpdate
    {
        [MenuItem("CONTEXT/LocalizeStringEvent/AutoFill")]
        public static void AddOnUpdateString(MenuCommand command)
        {
            var localizeStringEvent = (LocalizeStringEvent)command.context;
            var textMeshPro = localizeStringEvent.GetComponent<TextMeshProUGUI>();

            var count = localizeStringEvent.OnUpdateString.GetPersistentEventCount();
            for (var i = count - 1; i >= 0; i--)
            {
                UnityEventTools.RemovePersistentListener(localizeStringEvent.OnUpdateString, i);
            }
            
            var setStringMethod = textMeshPro.GetType().GetProperty("text").GetSetMethod();
            var methodDelegate = Delegate.CreateDelegate(typeof(UnityAction<string>), textMeshPro, setStringMethod) as UnityAction<string>;
            UnityEventTools.AddPersistentListener(localizeStringEvent.OnUpdateString, methodDelegate);
        }
    }
}